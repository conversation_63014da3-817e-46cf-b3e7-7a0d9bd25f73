<?php

namespace Tests\Feature\Employee;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class EmployeeAccessTest extends TestCase
{
    use RefreshDatabase;

    public function test_staff_user_can_access_employee_dashboard(): void
    {
        $staff = User::factory()->create([
            'role' => 'staff',
            'email_verified_at' => now(),
        ]);

        $response = $this->actingAs($staff)->get(route('employee.dashboard', absolute: false));

        $response->assertStatus(200);
    }

    public function test_guest_is_redirected_when_accessing_employee_dashboard(): void
    {
        $response = $this->get(route('employee.dashboard', absolute: false));

        // Employee routes use auth middleware; expect redirect to login
        $response->assertRedirect(route('login', absolute: false));
    }

    public function test_unverified_staff_can_access_employee_dashboard(): void
    {
        $staff = User::factory()->create([
            'role' => 'staff',
            'email_verified_at' => null,
        ]);

        $response = $this->actingAs($staff)->get(route('employee.dashboard', absolute: false));

        $response->assertStatus(200);
    }
}
