<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class RoleRedirectionTest extends TestCase
{
    use RefreshDatabase;

    public function test_admin_is_redirected_to_admin_dashboard_from_generic_dashboard(): void
    {
        $user = User::factory()->create(['role' => 'admin']);

        $response = $this->actingAs($user)->get('/dashboard');

        $response->assertRedirect(route('admin.dashboard', absolute: false));
    }

    public function test_vendor_is_redirected_to_vendor_dashboard_from_generic_dashboard(): void
    {
        $user = User::factory()->create(['role' => 'vendor']);

        $response = $this->actingAs($user)->get('/dashboard');

        $response->assertRedirect(route('vendor.dashboard', absolute: false));
    }

    public function test_staff_is_redirected_to_employee_dashboard_from_generic_dashboard(): void
    {
        $user = User::factory()->create(['role' => 'staff']);

        $response = $this->actingAs($user)->get('/dashboard');

        $response->assertRedirect(route('employee.dashboard', absolute: false));
    }
}
