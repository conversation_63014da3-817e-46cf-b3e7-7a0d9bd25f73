<?php

namespace Tests\Feature\Vendor;

use App\Models\User;
use App\Models\Vendor;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class VendorDashboardAccessTest extends TestCase
{
    use RefreshDatabase;

    public function test_guest_is_redirected_when_accessing_vendor_dashboard(): void
    {
        $response = $this->get(route('vendor.dashboard', absolute: false));

        $response->assertRedirect(route('login', absolute: false));
    }

    public function test_vendor_without_profile_is_denied_access(): void
    {
        $user = User::factory()->create(['role' => 'vendor']);

        // No Vendor record created for this user
        $response = $this->actingAs($user)->get(route('vendor.dashboard', absolute: false));

        $response->assertStatus(403);
    }

    public function test_vendor_can_access_vendor_dashboard_when_approved(): void
    {
        $user = User::factory()->create(['role' => 'vendor']);

        // Create a minimal approved vendor profile for the user
        Vendor::create([
            'user_id' => $user->id,
            'username' => 'vendor_user',
            'company_name' => 'Acme Co',
            'email' => $user->email,
            'is_approved' => true,
        ]);

        $response = $this->actingAs($user)->get(route('vendor.dashboard', absolute: false));

        $response->assertStatus(200);
    }

    public function test_unapproved_vendor_is_denied_access(): void
    {
        $user = User::factory()->create(['role' => 'vendor']);

        Vendor::create([
            'user_id' => $user->id,
            'username' => 'vendor_user2',
            'company_name' => 'Beta Co',
            'email' => $user->email,
            'is_approved' => false,
        ]);

        $response = $this->actingAs($user)->get(route('vendor.dashboard', absolute: false));

        $response->assertStatus(403);
    }
}
