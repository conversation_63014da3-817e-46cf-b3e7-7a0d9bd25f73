<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\GiftCampaign;
use App\Models\Gift;
use App\Models\MessageTemplate;
use App\Models\Client;
use App\Models\Vendor;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class GiftCampaignController extends Controller
{
    public function index()
    {
        $campaigns = GiftCampaign::with(['gift', 'messageTemplate', 'creator'])
            ->latest()
            ->paginate(15);

        $stats = [
            'total' => GiftCampaign::count(),
            'pending_approval' => GiftCampaign::pendingApproval()->count(),
            'in_progress' => GiftCampaign::inProgress()->count(),
            'completed' => GiftCampaign::completed()->count(),
        ];

        return view('admin.campaigns.index', compact('campaigns', 'stats'));
    }

    public function create()
    {
        $gifts = Gift::where('approved', true)->get();
        $vendors = Vendor::where('is_approved', true)->get(['id', 'name', 'email']);
        return view('admin.campaigns.create', compact('gifts', 'vendors'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'vendor_id' => 'required|exists:vendors,id',
            'gift_id' => 'required|exists:gifts,id',
        ]);

        $campaign = GiftCampaign::create([
            'vendor_id' => $request->vendor_id,
            'gift_id' => $request->gift_id,
            'created_by' => Auth::id(),
            'stage' => 'gift_info',
        ]);

        return redirect()->route('admin.campaigns.wizard', $campaign)
            ->with('success', 'تم إنشاء الحملة بنجاح. يرجى المتابعة مع الخطوات التالية.');
    }

    public function wizard(GiftCampaign $campaign)
    {
        $campaign->load(['gift', 'messageTemplate']);
        
        // Always load all required data for the wizard stages
        $gifts = Gift::where('approved', true)->get();
        $messageTemplates = MessageTemplate::all(); // Assuming we don't have active scope yet
        
        // Calculate filtered clients count for client filtering stage
        $filteredClientsCount = 0;
        if ($campaign->client_filters) {
            $clientQuery = Client::query();
            $filters = is_string($campaign->client_filters) ? json_decode($campaign->client_filters, true) : $campaign->client_filters;
            
            foreach ($filters as $key => $value) {
                if ($key === 'age_from') {
                    $clientQuery->where('age', '>=', $value);
                } elseif ($key === 'age_to') {
                    $clientQuery->where('age', '<=', $value);
                } else {
                    $clientQuery->where($key, $value);
                }
            }
            
            $filteredClientsCount = $clientQuery->count();
        } else {
            $filteredClientsCount = Client::count();
        }

        return view('admin.campaigns.wizard', compact('campaign', 'gifts', 'messageTemplates', 'filteredClientsCount'));
    }

    public function updateStage(Request $request, GiftCampaign $campaign)
    {
        switch ($campaign->stage) {
            case 'gift_info':
                return $this->updateGiftInfo($request, $campaign);
                
            case 'template_selection':
                return $this->updateTemplateSelection($request, $campaign);
                
            case 'client_filtering':
                return $this->updateClientFiltering($request, $campaign);
                
            case 'message_count':
                return $this->updateMessageCount($request, $campaign);
                
            case 'platform_selection':
                return $this->updatePlatformSelection($request, $campaign);
                
            case 'payment':
                return $this->updatePayment($request, $campaign);
        }

        return back()->with('error', 'مرحلة غير صحيحة.');
    }

    private function updateGiftInfo(Request $request, GiftCampaign $campaign)
    {
        $request->validate([
            'gift_id' => 'required|exists:gifts,id',
            'campaign_name' => 'required|string|max:255',
            'campaign_type' => 'required|in:promotional,seasonal,loyalty,birthday,appreciation',
            'campaign_description' => 'nullable|string|max:1000',
        ]);

        $campaign->update([
            'gift_id' => $request->gift_id,
            'campaign_name' => $request->campaign_name,
            'campaign_type' => $request->campaign_type,
            'campaign_description' => $request->campaign_description,
            'stage' => 'template_selection',
        ]);

        return redirect()->route('admin.campaigns.wizard', $campaign);
    }

    private function updateTemplateSelection(Request $request, GiftCampaign $campaign)
    {
        // Debug: Log the request data
        \Log::info('Template Selection Request Data:', $request->all());
        
        // Clean up request data based on template type
        if ($request->template_type === 'prebuilt') {
            // Remove custom fields from request when using prebuilt template
            $request->merge([
                'custom_message' => null,
                'custom_media_type' => null
            ]);
        } elseif ($request->template_type === 'custom') {
            // Remove template ID from request when using custom template
            $request->merge([
                'message_template_id' => null
            ]);
        }
        
        $rules = [
            'template_type' => 'required|in:prebuilt,custom',
            'custom_media_type' => 'nullable|in:audio,video,image',
            'custom_media_file' => 'nullable|file|mimes:jpg,jpeg,png,gif,mp4,mp3,wav|max:10240',
            'custom_message' => 'nullable|string',
        ];

        // Add conditional validation rules based on template type
        if ($request->template_type === 'prebuilt') {
            $rules['message_template_id'] = 'required|exists:message_templates,id';
        } else {
            $rules['custom_message'] = 'required|string|min:10';
        }

        $request->validate($rules);

        $updateData = [
            'template_type' => $request->template_type,
            'stage' => 'client_filtering',
        ];

        if ($request->template_type === 'prebuilt') {
            $updateData['message_template_id'] = $request->message_template_id;
            // Clear custom fields when selecting prebuilt template
            $updateData['custom_message'] = null;
            $updateData['custom_media_type'] = null;
            $updateData['custom_media_path'] = null;
        } else {
            $updateData['custom_message'] = $request->custom_message;
            $updateData['custom_media_type'] = $request->custom_media_type;
            // Clear prebuilt template when selecting custom
            $updateData['message_template_id'] = null;
            
            if ($request->hasFile('custom_media_file')) {
                $file = $request->file('custom_media_file');
                $filename = time() . '-' . $file->getClientOriginalName();
                $path = $file->storeAs('campaigns/media', $filename, 'public');
                $updateData['custom_media_path'] = '/storage/' . $path;
            }
        }

        // Debug: Log what we're trying to update
        \Log::info('Updating campaign with data:', $updateData);

        $campaign->update($updateData);

        return redirect()->route('admin.campaigns.wizard', $campaign);
    }

    private function updateClientFiltering(Request $request, GiftCampaign $campaign)
    {
        $request->validate([
            'country' => 'nullable|string',
            'city' => 'nullable|string',
            'neighborhood' => 'nullable|string',
            'gender' => 'nullable|in:ذكر,أنثى',
            'age_from' => 'nullable|integer|min:1|max:100',
            'age_to' => 'nullable|integer|min:1|max:100|gte:age_from',
            'health_condition' => 'nullable|string',
            'marital_status' => 'nullable|string',
        ]);

        // Build filters
        $filters = array_filter([
            'country' => $request->country,
            'city' => $request->city,
            'neighborhood' => $request->neighborhood,
            'gender' => $request->gender,
            'age_from' => $request->age_from,
            'age_to' => $request->age_to,
            'health_condition' => $request->health_condition,
            'marital_status' => $request->marital_status,
        ]);

        // Count matching clients
        $clientQuery = Client::query();
        
        foreach ($filters as $key => $value) {
            if ($key === 'age_from') {
                $clientQuery->where('age', '>=', $value);
            } elseif ($key === 'age_to') {
                $clientQuery->where('age', '<=', $value);
            } else {
                $clientQuery->where($key, $value);
            }
        }
        
        $targetClientCount = $clientQuery->count();

        $campaign->update([
            'client_filters' => $filters,
            'target_client_count' => $targetClientCount,
            'stage' => 'message_count',
        ]);

        return redirect()->route('admin.campaigns.wizard', $campaign);
    }

    private function updateMessageCount(Request $request, GiftCampaign $campaign)
    {
        $request->validate([
            'message_count' => 'required|integer|min:1|max:' . $campaign->target_client_count,
        ]);

        $campaign->update([
            'message_count' => $request->message_count,
            'stage' => 'platform_selection',
        ]);

        return redirect()->route('admin.campaigns.wizard', $campaign);
    }

    private function updatePlatformSelection(Request $request, GiftCampaign $campaign)
    {
        $request->validate([
            'platform' => 'required|in:own,external',
            'whatsapp_token' => 'required_if:platform,external|nullable|string',
            'messaging_channel' => 'required|in:whatsapp,email,sms',
        ]);

        $updateData = [
            'platform' => $request->platform,
            'messaging_channel' => $request->messaging_channel,
        ];

        if ($request->platform === 'external') {
            $updateData['whatsapp_token'] = $request->whatsapp_token;
            $updateData['requires_approval'] = true;
            $updateData['stage'] = 'pending_approval';
        } else {
            $updateData['total_cost'] = $campaign->calculateCost();
            $updateData['stage'] = 'payment';
        }

        $campaign->update($updateData);

        if ($request->platform === 'external') {
            return redirect()->route('admin.campaigns.pending')
                ->with('success', 'تم إرسال الحملة للموافقة. سيتم إشعارك عند الموافقة عليها.');
        }

        return redirect()->route('admin.campaigns.wizard', $campaign);
    }

    private function updatePayment(Request $request, GiftCampaign $campaign)
    {
        // In a real implementation, integrate with payment gateway
        $campaign->update([
            'payment_status' => 'paid',
            'payment_id' => 'DEMO_' . time(),
            'stage' => 'approved',
        ]);

        return redirect()->route('admin.campaigns.index')
            ->with('success', 'تم الدفع بنجاح. سيتم بدء الحملة قريباً.');
    }

    public function approve(GiftCampaign $campaign)
    {
        $campaign->update([
            'stage' => 'approved',
            'approved_by' => Auth::id(),
            'approved_at' => now(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم اعتماد الحملة بنجاح.',
        ]);
    }

    public function reject(GiftCampaign $campaign, Request $request)
    {
        $request->validate([
            'admin_notes' => 'required|string|max:1000',
        ]);

        $campaign->update([
            'stage' => 'failed',
            'admin_notes' => $request->admin_notes,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم رفض الحملة.',
        ]);
    }

    public function execute(GiftCampaign $campaign)
    {
        if ($campaign->stage !== 'approved') {
            return back()->with('error', 'لا يمكن تنفيذ هذه الحملة.');
        }

        // Start the campaign execution
        $campaign->update([
            'stage' => 'processing',
            'started_at' => now(),
        ]);

        // Here you would queue jobs to send messages
        // For demo purposes, we'll simulate execution
        
        return redirect()->route('admin.campaigns.index')
            ->with('success', 'تم بدء تنفيذ الحملة.');
    }

    public function show(GiftCampaign $campaign)
    {
        $campaign->load(['gift', 'messageTemplate', 'creator']);
        return view('admin.campaigns.show', compact('campaign'));
    }

    /**
     * Delete a campaign
     */
    public function destroy(GiftCampaign $campaign)
    {
        try {
            // Check if campaign can be deleted (only if not in processing or completed)
            if (in_array($campaign->stage, ['processing', 'completed'])) {
                return back()->with('error', 'لا يمكن حذف الحملة في هذه المرحلة.');
            }

            $campaignName = $campaign->campaign_name ?? 'حملة رقم ' . $campaign->id;
            
            // Delete associated files if any
            if ($campaign->custom_media_path) {
                $mediaPath = str_replace('/storage/', '', $campaign->custom_media_path);
                if (\Storage::disk('public')->exists($mediaPath)) {
                    \Storage::disk('public')->delete($mediaPath);
                }
            }

            // Delete the campaign
            $campaign->delete();

            return redirect()->route('admin.campaigns.index')
                ->with('success', 'تم حذف الحملة "' . $campaignName . '" بنجاح.');
                
        } catch (\Exception $e) {
            \Log::error('Campaign deletion error: ' . $e->getMessage());
            return back()->with('error', 'حدث خطأ أثناء حذف الحملة. يرجى المحاولة مرة أخرى.');
        }
    }

    /**
     * Go back to previous stage
     */
    public function goBackStage(GiftCampaign $campaign)
    {
        $stageOrder = [
            'gift_info',
            'template_selection', 
            'client_filtering',
            'message_count',
            'platform_selection',
            'payment',
            'pending_approval'
        ];

        $currentIndex = array_search($campaign->stage, $stageOrder);
        
        if ($currentIndex === false || $currentIndex <= 0) {
            return back()->with('error', 'لا يمكن العودة للمرحلة السابقة.');
        }

        // Prevent going back from certain stages
        if (in_array($campaign->stage, ['approved', 'processing', 'completed', 'failed'])) {
            return back()->with('error', 'لا يمكن العودة للمرحلة السابقة من هذه المرحلة.');
        }

        $previousStage = $stageOrder[$currentIndex - 1];
        
        $campaign->update([
            'stage' => $previousStage
        ]);

        return redirect()->route('admin.campaigns.wizard', $campaign)
            ->with('success', 'تم الانتقال للمرحلة السابقة بنجاح.');
    }

    public function pending()
    {
        $campaigns = GiftCampaign::with(['gift', 'messageTemplate', 'creator'])
            ->where('stage', 'pending_approval')
            ->latest()
            ->paginate(15);

        return view('admin.campaigns.pending', compact('campaigns'));
    }

    /**
     * Financial Reports for Vendors
     */
    public function financialReports(Request $request)
    {
        $query = GiftCampaign::with(['gift.vendor', 'messageTemplate'])
            ->whereHas('gift.vendor');

        // Filter by vendor if specified
        if ($request->filled('vendor_id')) {
            $query->whereHas('gift', function($q) use ($request) {
                $q->where('vendor_id', $request->vendor_id);
            });
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Filter by campaign status
        if ($request->filled('status')) {
            $query->where('stage', $request->status);
        }

        $campaigns = $query->latest()->paginate(15);

        // Calculate totals for each vendor
        $vendorReports = [];
        foreach ($campaigns as $campaign) {
            $vendorId = $campaign->gift->vendor->id;
            
            if (!isset($vendorReports[$vendorId])) {
                $vendorReports[$vendorId] = [
                    'vendor' => $campaign->gift->vendor,
                    'campaigns_count' => 0,
                    'total_messages_sent' => 0,
                    'total_campaign_cost' => 0,
                    'total_gifts_claimed' => 0,
                    'commission_rate' => 15, // 15% commission rate
                    'total_commission' => 0,
                    'amount_paid' => 0,
                    'amount_due' => 0,
                    'campaigns' => []
                ];
            }

            $vendorReports[$vendorId]['campaigns_count']++;
            $vendorReports[$vendorId]['total_messages_sent'] += $campaign->sent_count;
            $vendorReports[$vendorId]['total_campaign_cost'] += $campaign->total_cost;
            $vendorReports[$vendorId]['total_gifts_claimed'] += $campaign->gifts_claimed_count ?? 0;
            
            // Calculate commission (15% of gift value * claimed gifts)
            $giftCommission = ($campaign->gift->price * ($vendorReports[$vendorId]['commission_rate'] / 100)) * ($campaign->gifts_claimed_count ?? 0);
            $vendorReports[$vendorId]['total_commission'] += $giftCommission;
            
            // For now, amount paid is the campaign cost, amount due is the commission
            $vendorReports[$vendorId]['amount_paid'] += $campaign->total_cost;
            $vendorReports[$vendorId]['amount_due'] += $giftCommission;
            
            $vendorReports[$vendorId]['campaigns'][] = $campaign;
        }

        // Get all vendors for filter dropdown
        $vendors = \App\Models\Vendor::where('approved', true)->get();

        return view('admin.campaigns.financial-reports', compact('vendorReports', 'campaigns', 'vendors'));
    }

    /**
     * Vendor Financial Details
     */
    public function vendorFinancialDetails($vendorId)
    {
        $vendor = \App\Models\Vendor::findOrFail($vendorId);
        
        $campaigns = GiftCampaign::with(['gift', 'messageTemplate'])
            ->whereHas('gift', function($q) use ($vendorId) {
                $q->where('vendor_id', $vendorId);
            })
            ->latest()
            ->get();

        $summary = [
            'total_campaigns' => $campaigns->count(),
            'active_campaigns' => $campaigns->where('stage', 'processing')->count(),
            'completed_campaigns' => $campaigns->where('stage', 'completed')->count(),
            'total_messages_sent' => $campaigns->sum('sent_count'),
            'total_campaign_costs' => $campaigns->sum('total_cost'),
            'total_gifts_claimed' => $campaigns->sum('gifts_claimed_count'),
            'commission_rate' => 15,
            'total_commission_earned' => 0,
            'total_amount_paid' => $campaigns->sum('total_cost'),
            'pending_commissions' => 0
        ];

        // Calculate commissions
        foreach ($campaigns as $campaign) {
            $giftCommission = ($campaign->gift->price * 0.15) * ($campaign->gifts_claimed_count ?? 0);
            $summary['total_commission_earned'] += $giftCommission;
            
            if ($campaign->stage !== 'completed') {
                $summary['pending_commissions'] += $giftCommission;
            }
        }

        return view('admin.campaigns.vendor-financial-details', compact('vendor', 'campaigns', 'summary'));
    }

    public function getFilteredClients(Request $request)
    {
        $filters = $request->all();
        
        $query = Client::query();
        
        foreach ($filters as $key => $value) {
            if (empty($value)) continue;
            
            if ($key === 'age_from') {
                $query->where('age', '>=', $value);
            } elseif ($key === 'age_to') {
                $query->where('age', '<=', $value);
            } else {
                $query->where($key, $value);
            }
        }
        
        $count = $query->count();
        
        return response()->json([
            'count' => $count,
            'message' => "يوجد {$count} عميل يطابق المعايير المحددة"
        ]);
    }

    /**
     * Get gifts for a specific vendor
     */
    public function getVendorGifts($vendorId)
    {
        $gifts = Gift::where('vendor_id', $vendorId)
                    ->where('approved', true)
                    ->get(['id', 'name', 'price', 'stock_quantity', 'description']);
        
        return response()->json($gifts);
    }
}
