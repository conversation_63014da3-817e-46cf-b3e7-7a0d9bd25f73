<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'لوحة التاجر') - هدايا السعودية</title>

    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --vendor-primary: #6a1b9a;
            --vendor-secondary: #ff1493;
            --vendor-accent: #ff9800;
            --sidebar-width: 280px;
            --navbar-height: 70px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, var(--vendor-primary) 0%, #1a1a1a 100%);
            color: #e0e0e0;
            overflow-x: hidden;
        }

        /* Professional Vendor Sidebar */
        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            width: var(--sidebar-width);
            height: 100vh;
            background: linear-gradient(180deg, var(--vendor-primary) 0%, #4a148c 100%);
            backdrop-filter: blur(20px);
            border-left: 1px solid rgba(255, 20, 147, 0.2);
            z-index: 1000;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            box-shadow: -10px 0 30px rgba(0, 0, 0, 0.5);
        }

        .sidebar.collapsed {
            width: 80px;
        }

        /* Vendor Sidebar Header */
        .sidebar-header {
            height: var(--navbar-height);
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, var(--vendor-secondary) 0%, var(--vendor-accent) 100%);
            position: relative;
            overflow: hidden;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            color: white;
            text-decoration: none;
            font-weight: 800;
            font-size: 1.2rem;
            z-index: 2;
            position: relative;
        }

        .sidebar-logo i {
            font-size: 2rem;
            margin-left: 15px;
        }

        .sidebar.collapsed .logo-text {
            display: none;
        }

        /* Navigation Menu */
        .sidebar-nav {
            padding: 20px 0;
            height: calc(100vh - var(--navbar-height));
            overflow-y: auto;
            overflow-x: hidden;
        }

        .nav-section {
            margin-bottom: 30px;
        }

        .nav-section-title {
            color: #b39ddb;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            padding: 0 25px;
            margin-bottom: 15px;
        }

        .sidebar.collapsed .nav-section-title {
            display: none;
        }

        .nav-item {
            margin-bottom: 5px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 15px 25px;
            color: #e1bee7;
            text-decoration: none;
            transition: all 0.3s ease;
            position: relative;
            border-radius: 0 25px 25px 0;
            margin-left: 15px;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 4px;
            height: 100%;
            background: var(--vendor-secondary);
            transform: scaleY(0);
            transition: transform 0.3s ease;
        }

        .nav-link:hover,
        .nav-link.active {
            color: white;
            background: linear-gradient(135deg, rgba(255, 20, 147, 0.2) 0%, rgba(255, 20, 147, 0.05) 100%);
            transform: translateX(-5px);
        }

        .nav-link:hover::before,
        .nav-link.active::before {
            transform: scaleY(1);
        }

        .nav-link i {
            font-size: 1.25rem;
            width: 25px;
            margin-left: 15px;
            transition: all 0.3s ease;
        }

        .nav-link:hover i {
            transform: scale(1.1);
            color: var(--vendor-secondary);
        }

        .sidebar.collapsed .nav-link {
            justify-content: center;
            padding: 15px;
        }

        .sidebar.collapsed .nav-link span {
            display: none;
        }

        .sidebar.collapsed .nav-link i {
            margin-left: 0;
        }

        /* Badge for notifications */
        .nav-badge {
            background: var(--vendor-secondary);
            color: white;
            font-size: 0.7rem;
            padding: 2px 6px;
            border-radius: 10px;
            margin-right: auto;
        }

        .sidebar.collapsed .nav-badge {
            display: none;
        }

        /* Professional Navbar */
        .main-navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: var(--sidebar-width);
            height: var(--navbar-height);
            background: linear-gradient(135deg, #2d2d2d 0%, var(--vendor-primary) 100%);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 20, 147, 0.2);
            z-index: 999;
            display: flex;
            align-items: center;
            padding: 0 30px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
        }

        .sidebar.collapsed ~ .main-content .main-navbar {
            right: 80px;
        }

        .navbar-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .sidebar-toggle {
            background: none;
            border: none;
            color: #e1bee7;
            font-size: 1.5rem;
            cursor: pointer;
            padding: 10px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .sidebar-toggle:hover {
            color: var(--vendor-secondary);
            background: rgba(255, 20, 147, 0.1);
        }

        .breadcrumb-nav {
            display: flex;
            align-items: center;
            gap: 10px;
            color: #b39ddb;
        }

        .breadcrumb-nav a {
            color: var(--vendor-secondary);
            text-decoration: none;
        }

        .navbar-right {
            margin-right: auto;
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .navbar-actions {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .action-btn {
            position: relative;
            background: none;
            border: none;
            color: #e1bee7;
            font-size: 1.2rem;
            padding: 10px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            color: var(--vendor-secondary);
            background: rgba(255, 20, 147, 0.1);
        }

        .action-btn .badge {
            position: absolute;
            top: 5px;
            left: 5px;
            background: var(--vendor-secondary);
            color: white;
            font-size: 0.6rem;
            padding: 2px 5px;
            border-radius: 8px;
            min-width: 16px;
            text-align: center;
        }

        .user-dropdown {
            position: relative;
            margin-right: 10px;
        }

        .user-avatar {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--vendor-secondary) 0%, var(--vendor-accent) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 700;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid rgba(255, 20, 147, 0.3);
        }

        .user-avatar:hover {
            transform: scale(1.1);
            box-shadow: 0 5px 15px rgba(255, 20, 147, 0.4);
        }

        /* Main Content */
        .main-content {
            margin-right: var(--sidebar-width);
            min-height: 100vh;
            transition: all 0.3s ease;
        }

        .sidebar.collapsed ~ .main-content {
            margin-right: 80px;
        }

        .page-content {
            padding: calc(var(--navbar-height) + 30px) 30px 30px;
            min-height: 100vh;
        }

        /* Professional Create Form Styles */
        .create-form-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            position: relative;
        }

        .create-form-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, var(--vendor-secondary) 0%, var(--vendor-accent) 100%);
        }

        .create-form-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 30px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            text-align: center;
            position: relative;
        }

        .create-form-header h1 {
            color: var(--vendor-primary);
            font-weight: 700;
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .create-form-header p {
            color: #6c757d;
            font-size: 1.1rem;
            margin: 0;
        }

        .create-form-body {
            padding: 40px;
            color: #212529;
        }

        .form-section {
            margin-bottom: 40px;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 15px;
            border-left: 4px solid var(--vendor-secondary);
            position: relative;
            transition: all 0.3s ease;
        }

        .form-section:hover {
            box-shadow: 0 10px 30px rgba(106, 27, 154, 0.1);
            transform: translateY(-2px);
        }

        .form-section-title {
            color: var(--vendor-primary);
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }

        .form-section-title i {
            margin-left: 10px;
            color: var(--vendor-secondary);
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-label {
            color: #495057;
            font-weight: 600;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
        }

        .form-label i {
            margin-left: 8px;
            color: var(--vendor-secondary);
        }

        .form-control, .form-select {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 12px 16px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
            color: #495057;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--vendor-secondary);
            box-shadow: 0 0 20px rgba(255, 20, 147, 0.2);
            outline: none;
        }

        .form-control::placeholder {
            color: #adb5bd;
        }

        .btn {
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            border: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--vendor-secondary) 0%, var(--vendor-primary) 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(255, 20, 147, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #00d4aa 0%, #00a085 100%);
            color: white;
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(0, 212, 170, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(108, 117, 125, 0.4);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
            box-shadow: none !important;
        }

        .alert {
            border-radius: 15px;
            border: none;
            padding: 20px;
            margin-bottom: 25px;
        }

        .alert-info {
            background: linear-gradient(135deg, rgba(56, 103, 214, 0.1) 0%, rgba(56, 103, 214, 0.05) 100%);
            border-left: 4px solid #3867d6;
            color: #1e3a8a;
        }

        .alert-danger {
            background: linear-gradient(135deg, rgba(255, 71, 87, 0.1) 0%, rgba(255, 71, 87, 0.05) 100%);
            border-left: 4px solid #ff4757;
            color: #dc2626;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-right: 0;
            }
            
            .main-navbar {
                right: 0;
            }
            
            .create-form-body {
                padding: 20px;
            }
            
            .form-section {
                padding: 20px;
            }
        }
    </style>

    @stack('styles')
</head>
<body>
    <!-- Vendor Sidebar -->
    <aside class="sidebar" id="sidebar">
        <!-- Sidebar Header -->
        <div class="sidebar-header">
            <a href="{{ route('vendor.dashboard') }}" class="sidebar-logo">
                <i class="fas fa-store"></i>
                <span class="logo-text">لوحة التاجر</span>
            </a>
        </div>

        <!-- Navigation Menu -->
        <nav class="sidebar-nav">
            <!-- Dashboard Section -->
            <div class="nav-section">
                <div class="nav-section-title">الرئيسية</div>
                <div class="nav-item">
                    <a href="{{ route('vendor.dashboard') }}" class="nav-link {{ request()->routeIs('vendor.dashboard') ? 'active' : '' }}">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>لوحة التحكم</span>
                    </a>
                </div>
            </div>

            <!-- Business Management -->
            <div class="nav-section">
                <div class="nav-section-title">إدارة الأعمال</div>
                
                <div class="nav-item">
                    <a href="{{ route('vendor.gifts.index') }}" class="nav-link {{ request()->routeIs('vendor.gifts.*') ? 'active' : '' }}">
                        <i class="fas fa-gift"></i>
                        <span>هداياي</span>
                    </a>
                </div>

                <div class="nav-item">
                    <a href="{{ route('vendor.campaigns.index') }}" class="nav-link {{ request()->routeIs('vendor.campaigns.*') ? 'active' : '' }}">
                        <i class="fas fa-bullhorn"></i>
                        <span>حملاتي الإعلانية</span>
                    </a>
                </div>

                <div class="nav-item">
                    <a href="{{ route('vendor.employees.index') }}" class="nav-link {{ request()->routeIs('vendor.employees.*') ? 'active' : '' }}">
                        <i class="fas fa-users"></i>
                        <span>الموظفين</span>
                    </a>
                </div>
            </div>

            <!-- Account Section -->
            <div class="nav-section">
                <div class="nav-item">
                    <form method="POST" action="{{ route('logout') }}" class="d-inline">
                        @csrf
                        <button type="submit" class="nav-link border-0 bg-transparent w-100 text-start">
                            <i class="fas fa-sign-out-alt"></i>
                            <span>تسجيل الخروج</span>
                        </button>
                    </form>
                </div>
            </div>
        </nav>
    </aside>

    <!-- Main Content Area -->
    <div class="main-content">
        <!-- Top Navbar -->
        <nav class="main-navbar">
            <div class="navbar-left">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                
                <div class="breadcrumb-nav">
                    <a href="{{ route('vendor.dashboard') }}">الرئيسية</a>
                    @if(!request()->routeIs('vendor.dashboard'))
                        <i class="fas fa-chevron-left"></i>
                        <span>@yield('page-title', 'الصفحة')</span>
                    @endif
                </div>
            </div>

            <div class="navbar-right">
                <!-- Action Buttons -->
                <div class="navbar-actions">
                    <button class="action-btn" title="الإشعارات">
                        <i class="fas fa-bell"></i>
                        <span class="badge">2</span>
                    </button>
                    
                    <button class="action-btn" title="الرسائل">
                        <i class="fas fa-envelope"></i>
                    </button>
                    
                    <button class="action-btn" title="الإعدادات">
                        <i class="fas fa-cog"></i>
                    </button>
                </div>

                <!-- User Profile -->
                <div class="user-dropdown">
                    <div class="user-avatar" title="{{ auth()->user()->name ?? 'التاجر' }}">
                        {{ substr(auth()->user()->name ?? 'ت', 0, 1) }}
                    </div>
                </div>
            </div>
        </nav>

        <!-- Page Content -->
        <main class="page-content">
            @yield('content')
        </main>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Sidebar Toggle
            const sidebar = document.getElementById('sidebar');
            const sidebarToggle = document.getElementById('sidebarToggle');
            
            sidebarToggle.addEventListener('click', function() {
                sidebar.classList.toggle('collapsed');
                localStorage.setItem('vendor-sidebar-collapsed', sidebar.classList.contains('collapsed'));
            });

            // Restore sidebar state
            if (localStorage.getItem('vendor-sidebar-collapsed') === 'true') {
                sidebar.classList.add('collapsed');
            }

            // Mobile responsive
            function handleResize() {
                if (window.innerWidth <= 768) {
                    sidebar.classList.add('collapsed');
                } else {
                    sidebar.classList.remove('show');
                }
            }

            // Mobile menu toggle
            if (window.innerWidth <= 768) {
                sidebarToggle.addEventListener('click', function(e) {
                    e.stopPropagation();
                    sidebar.classList.toggle('show');
                });

                // Close sidebar when clicking outside
                document.addEventListener('click', function(e) {
                    if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
                        sidebar.classList.remove('show');
                    }
                });
            }

            window.addEventListener('resize', handleResize);
            handleResize();
        });
    </script>

    @stack('scripts')
</body>
</html> 