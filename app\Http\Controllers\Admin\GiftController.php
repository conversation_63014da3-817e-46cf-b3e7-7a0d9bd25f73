<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Gift;
use App\Models\Vendor;
use Illuminate\Http\Request;

class GiftController extends Controller
{
    public function index(Request $request)
    {
        $query = Gift::with(['vendor', 'vendor.user']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('category', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by approval
        if ($request->filled('approved')) {
            $query->where('approved', $request->approved === '1');
        }

        // Filter by vendor
        if ($request->filled('vendor_id')) {
            $query->where('vendor_id', $request->vendor_id);
        }

        // Filter by category
        if ($request->filled('category')) {
            $query->where('category', $request->category);
        }

        $gifts = $query->latest()->paginate(15);

        // Get filter options
        $vendors = Vendor::with('user')->where('is_approved', true)->get();
        $categories = Gift::whereNotNull('category')->distinct()->pluck('category')->filter();
        $statuses = ['قيد الانتظار', 'تم التسليم', 'تم الرفض'];

        // Statistics
        $stats = [
            'total' => Gift::count(),
            'approved' => Gift::where('approved', true)->count(),
            'pending' => Gift::where('approved', false)->count(),
            'delivered' => Gift::where('status', 'تم التسليم')->count(),
            'available' => Gift::where('status', 'قيد الانتظار')->count(), // Using actual status from DB
        ];

        return view('admin.gifts.index', compact('gifts', 'vendors', 'categories', 'statuses', 'stats'));
    }

    public function show(Gift $gift)
    {
        $gift->load(['vendor', 'vendor.user']);
        return view('admin.gifts.show', compact('gift'));
    }

    public function create()
    {
        $vendors = Vendor::with('user')->where('is_approved', true)->get();
        return view('admin.gifts.create', compact('vendors'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'vendor_id' => 'required|exists:vendors,id',
            'category' => 'nullable|string|max:255',
            'status' => 'nullable|in:قيد الانتظار,تم التسليم,تم الرفض',
            'image' => 'required|image|mimes:jpeg,jpg,png,gif|max:2048',
            'approved' => 'boolean',
            'notes' => 'nullable|string|max:1000',
        ]);

        $data = $request->all();
        $data['status'] = $data['status'] ?? 'قيد الانتظار';
        $data['approved'] = $request->has('approved');

        // Handle image upload
        if ($request->hasFile('image')) {
            $image = $request->file('image');
            $filename = time() . '-' . $image->getClientOriginalName();
            $path = $image->storeAs('gifts', $filename, 'public');
            $data['image'] = '/storage/' . $path;
        }

        // Handle save as draft
        if ($request->has('save_as_draft')) {
            $data['status'] = 'مسودة';
            $data['approved'] = false;
        }

        Gift::create($data);

        $message = $request->has('save_as_draft') ? 'تم حفظ الهدية كمسودة بنجاح' : 'تم إنشاء الهدية بنجاح';

        return redirect()->route('admin.gifts.index')->with('success', $message);
    }

    public function edit(Gift $gift)
    {
        $vendors = Vendor::with('user')->where('is_approved', true)->get();
        $categories = ['هدايا شخصية', 'هدايا أطفال', 'هدايا أعياد', 'زهور', 'حلويات', 'أخرى'];
        
        return view('admin.gifts.edit', compact('gift', 'vendors', 'categories'));
    }

    public function update(Request $request, Gift $gift)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'category' => 'nullable|string|max:100',
            'status' => 'required|in:قيد الانتظار,تم التسليم,تم الرفض',
            'approved' => 'boolean',
            'notes' => 'nullable|string|max:1000',
        ]);

        $data = $request->all();
        $data['approved'] = $request->has('approved');

        // Handle image upload
        if ($request->hasFile('image')) {
            $image = $request->file('image');
            $filename = time() . '-' . $image->getClientOriginalName();
            $path = $image->storeAs('gifts', $filename, 'public');
            $data['image'] = '/storage/' . $path;
        }

        $gift->update($data);

        return redirect()->route('admin.gifts.index')
                        ->with('success', 'تم تحديث الهدية بنجاح.');
    }

    public function destroy(Gift $gift)
    {
        $gift->delete();

        return redirect()->route('admin.gifts.index')
                        ->with('success', 'تم حذف الهدية بنجاح.');
    }

    public function approve(Gift $gift)
    {
        $gift->update(['approved' => true]);

        return response()->json([
            'success' => true,
            'message' => 'تم اعتماد الهدية بنجاح.'
        ]);
    }

    public function reject(Gift $gift)
    {
        $gift->update(['approved' => false]);

        return response()->json([
            'success' => true,
            'message' => 'تم رفض الهدية.'
        ]);
    }

    public function bulkAction(Request $request)
    {
        $request->validate([
            'gifts' => 'required|array',
            'action' => 'required|in:approve,reject,delete',
        ]);

        $gifts = Gift::whereIn('id', $request->gifts);

        switch ($request->action) {
            case 'approve':
                $gifts->update(['approved' => true]);
                $message = 'تم اعتماد الهدايا المحددة بنجاح.';
                break;
            case 'reject':
                $gifts->update(['approved' => false]);
                $message = 'تم رفض الهدايا المحددة.';
                break;
            case 'delete':
                $gifts->delete();
                $message = 'تم حذف الهدايا المحددة بنجاح.';
                break;
        }

        return response()->json([
            'success' => true,
            'message' => $message
        ]);
    }
}
