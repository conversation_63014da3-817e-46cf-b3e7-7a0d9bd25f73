<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Client;
use App\Models\Gift;
use App\Models\GiftCampaign;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\ClientsExport;
use App\Imports\ClientsImport;

class ClientController extends Controller
{
    /**
     * Display a listing of the clients
     */
    public function index(Request $request)
    {
        $query = Client::with(['gift', 'campaign']);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('city', 'like', "%{$search}%");
            });
        }

        if ($request->filled('gender')) {
            $query->gender($request->gender);
        }

        if ($request->filled('age_from') && $request->filled('age_to')) {
            $query->ageRange($request->age_from, $request->age_to);
        }

        if ($request->filled('health_condition')) {
            $query->healthCondition($request->health_condition);
        }

        if ($request->filled('country')) {
            $query->where('country', $request->country);
        }

        if ($request->filled('city')) {
            $query->where('city', $request->city);
        }

        if ($request->filled('payment_status')) {
            $query->where('payment_status', $request->payment_status);
        }

        if ($request->filled('message_received')) {
            if ($request->message_received === '1') {
                $query->messageReceived();
            } else {
                $query->where('message_received', false);
            }
        }

        if ($request->filled('gift_received')) {
            if ($request->gift_received === '1') {
                $query->giftReceived();
            } else {
                $query->where('gift_received', false);
            }
        }

        if ($request->filled('gift_id')) {
            $query->where('gift_id', $request->gift_id);
        }

        // Sort
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $clients = $query->paginate(20)->withQueryString();

        // Get filter options
        $gifts = Gift::approved()->get(['id', 'name']);
        $countries = Client::whereNotNull('country')->distinct()->pluck('country')->sort();
        $cities = Client::whereNotNull('city')->distinct()->pluck('city')->sort();
        $healthConditions = Client::whereNotNull('health_condition')->distinct()->pluck('health_condition')->sort();

        $stats = [
            'total' => Client::count(),
            'message_received' => Client::messageReceived()->count(),
            'gift_received' => Client::giftReceived()->count(),
            'paid' => Client::paid()->count(),
            'male' => Client::gender('ذكر')->count(),
            'female' => Client::gender('أنثى')->count(),
        ];

        // Get all dynamic fields from existing clients to display as columns
        $dynamicFields = [];
        $clientsWithFields = Client::whereNotNull('additional_fields')->get();
        
        foreach ($clientsWithFields as $client) {
            if ($client->additional_fields && is_array($client->additional_fields)) {
                foreach ($client->additional_fields as $key => $value) {
                    if (!in_array($key, $dynamicFields)) {
                        $dynamicFields[] = $key;
                    }
                }
            }
        }

        return view('admin.clients.index', compact(
            'clients', 'gifts', 'countries', 'cities', 'healthConditions', 'stats', 'dynamicFields'
        ));
    }

    /**
     * Show the form for creating a new client
     */
    public function create()
    {
        $gifts = Gift::approved()->get(['id', 'name']);
        $campaigns = GiftCampaign::active()->get(['id', 'name']);
        
        // Get dynamic fields to show in form
        $dynamicFields = [];
        $clientsWithFields = Client::whereNotNull('additional_fields')->get();
        
        foreach ($clientsWithFields as $client) {
            if ($client->additional_fields && is_array($client->additional_fields)) {
                foreach ($client->additional_fields as $key => $value) {
                    if (!in_array($key, $dynamicFields)) {
                        $dynamicFields[] = $key;
                    }
                }
            }
        }
        
        return view('admin.clients.create', compact('gifts', 'campaigns', 'dynamicFields'));
    }

    /**
     * Store a newly created client
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'phone' => 'required|string|max:20|unique:clients',
            'email' => 'nullable|email|max:255|unique:clients',
            'country' => 'required|string|max:255',
            'city' => 'required|string|max:255',
            'neighborhood' => 'nullable|string|max:255',
            'gender' => 'required|in:ذكر,أنثى',
            'age' => 'required|integer|min:1|max:120',
            'health_condition' => 'nullable|string|max:255',
            'birthdate' => 'nullable|date',
            'occupation' => 'nullable|string|max:255',
            'gift_id' => 'nullable|exists:gifts,id',
            'campaign_id' => 'nullable|exists:gift_campaigns,id',
            'amount_paid' => 'nullable|numeric|min:0',
            'total_amount' => 'nullable|numeric|min:0',
            'paid_amount' => 'nullable|numeric|min:0',
            'remaining_amount' => 'nullable|numeric|min:0',
            'payment_method' => 'nullable|string|max:255',
            'payment_status' => 'nullable|in:pending,paid,failed,refunded',
            'assigned_employee' => 'nullable|string|max:255',
            'message_received' => 'boolean',
            'gift_received' => 'boolean',
            'rating' => 'nullable|integer|min:1|max:5',
            'feedback' => 'nullable|string|max:1000',
        ]);

        $data = $request->all();
        
        // Handle additional dynamic fields
        $additionalFields = [];
        foreach ($request->all() as $key => $value) {
            if (strpos($key, 'custom_') === 0 && !empty($value)) {
                $fieldName = str_replace('custom_', '', $key);
                $additionalFields[$fieldName] = $value;
            }
        }
        
        if (!empty($additionalFields)) {
            $data['additional_fields'] = $additionalFields;
        }

        Client::create($data);

        return redirect()->route('admin.clients.index')
            ->with('success', 'تم إنشاء العميل بنجاح');
    }

    /**
     * Display the specified client
     */
    public function show(Client $client)
    {
        $client->load(['gift', 'campaign']);
        
        return view('admin.clients.show', compact('client'));
    }

    /**
     * Show the form for editing the client
     */
    public function edit(Client $client)
    {
        $gifts = Gift::approved()->get(['id', 'name']);
        $campaigns = GiftCampaign::active()->get(['id', 'name']);
        
        // Get dynamic fields to show in form
        $dynamicFields = [];
        $clientsWithFields = Client::whereNotNull('additional_fields')->get();
        
        foreach ($clientsWithFields as $clientField) {
            if ($clientField->additional_fields && is_array($clientField->additional_fields)) {
                foreach ($clientField->additional_fields as $key => $value) {
                    if (!in_array($key, $dynamicFields)) {
                        $dynamicFields[] = $key;
                    }
                }
            }
        }
        
        return view('admin.clients.edit', compact('client', 'gifts', 'campaigns', 'dynamicFields'));
    }

    /**
     * Update the specified client
     */
    public function update(Request $request, Client $client)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'phone' => 'required|string|max:20|unique:clients,phone,' . $client->id,
            'email' => 'nullable|email|max:255|unique:clients,email,' . $client->id,
            'country' => 'required|string|max:255',
            'city' => 'required|string|max:255',
            'neighborhood' => 'nullable|string|max:255',
            'gender' => 'required|in:ذكر,أنثى',
            'age' => 'required|integer|min:1|max:120',
            'health_condition' => 'nullable|string|max:255',
            'birthdate' => 'nullable|date',
            'occupation' => 'nullable|string|max:255',
            'gift_id' => 'nullable|exists:gifts,id',
            'campaign_id' => 'nullable|exists:gift_campaigns,id',
            'amount_paid' => 'nullable|numeric|min:0',
            'total_amount' => 'nullable|numeric|min:0',
            'paid_amount' => 'nullable|numeric|min:0',
            'remaining_amount' => 'nullable|numeric|min:0',
            'payment_method' => 'nullable|string|max:255',
            'payment_status' => 'nullable|in:pending,paid,failed,refunded',
            'assigned_employee' => 'nullable|string|max:255',
            'message_received' => 'boolean',
            'gift_received' => 'boolean',
            'rating' => 'nullable|integer|min:1|max:5',
            'feedback' => 'nullable|string|max:1000',
        ]);

        $data = $request->all();
        
        // Handle additional dynamic fields
        $additionalFields = $client->additional_fields ?? [];
        foreach ($request->all() as $key => $value) {
            if (strpos($key, 'custom_') === 0) {
                $fieldName = str_replace('custom_', '', $key);
                if (!empty($value)) {
                    $additionalFields[$fieldName] = $value;
                } else {
                    unset($additionalFields[$fieldName]);
                }
            }
        }
        
        $data['additional_fields'] = $additionalFields;

        $client->update($data);

        return redirect()->route('admin.clients.show', $client)
            ->with('success', 'تم تحديث بيانات العميل بنجاح');
    }

    /**
     * Remove the specified client
     */
    public function destroy(Client $client)
    {
        $client->delete();

        return redirect()->route('admin.clients.index')
            ->with('success', 'تم حذف العميل بنجاح');
    }

    /**
     * Update client status
     */
    public function updateStatus(Request $request, Client $client)
    {
        $request->validate([
            'status' => 'required|in:active,inactive,pending'
        ]);

        $client->update(['status' => $request->status]);

        return response()->json([
            'success' => true,
            'message' => 'تم تحديث حالة العميل بنجاح'
        ]);
    }

    /**
     * Mark client as paid
     */
    public function markAsPaid(Client $client)
    {
        $client->update([
            'payment_status' => 'paid',
            'paid_amount' => $client->total_amount,
            'remaining_amount' => 0,
            'payment_date' => now()
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم تسديد العميل بنجاح'
        ]);
    }

    /**
     * Export clients to Excel (renamed from exportExcel)
     */
    public function export(Request $request)
    {
        $query = Client::with(['gift', 'campaign']);

        // Apply same filters as index
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('city', 'like', "%{$search}%");
            });
        }

        // Apply all other filters...
        if ($request->filled('gender')) {
            $query->gender($request->gender);
        }

        if ($request->filled('country')) {
            $query->where('country', $request->country);
        }

        // Get all matching clients
        $clients = $query->get();

        return Excel::download(new ClientsExport($clients), 'clients-' . now()->format('Y-m-d') . '.xlsx');
    }

    /**
     * Import clients from Excel with enhanced feedback
     */
    public function import(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:xlsx,xls,csv|max:5120' // Increased file size limit
        ]);

        try {
            $import = new ClientsImport;
            Excel::import($import, $request->file('file'));

            $importedCount = $import->getImportedCount();
            $skippedCount = $import->getSkippedCount();
            $errors = $import->getErrors();

            $message = "تم استيراد {$importedCount} عميل بنجاح";
            
            if ($skippedCount > 0) {
                $message .= " وتم تخطي {$skippedCount} صف";
            }

            // Log detailed results
            \Log::info('Client import completed', [
                'imported' => $importedCount,
                'skipped' => $skippedCount,
                'errors' => $errors
            ]);

            $alertType = $skippedCount > 0 ? 'warning' : 'success';
            
            return redirect()->route('admin.clients.index')
                ->with($alertType, $message)
                ->with('import_details', [
                    'imported' => $importedCount,
                    'skipped' => $skippedCount,
                    'errors' => array_slice($errors, 0, 10) // Show only first 10 errors
                ]);

        } catch (\Maatwebsite\Excel\Validators\ValidationException $e) {
            $failures = $e->failures();
            $errorMessages = [];
            
            foreach ($failures as $failure) {
                $errorMessages[] = "Row {$failure->row()}: " . implode(', ', $failure->errors());
            }
            
            return back()->with('error', 'فشل في التحقق من البيانات: ' . implode(' | ', array_slice($errorMessages, 0, 5)));
            
        } catch (\Exception $e) {
            \Log::error('Import error: ' . $e->getMessage());
            return back()->with('error', 'حدث خطأ أثناء الاستيراد: ' . $e->getMessage());
        }
    }

    /**
     * Download Excel template for client import
     */
    public function downloadTemplate()
    {
        $headers = [
            'الاسم', // name (required)
            'الهاتف', // phone (required)
            'البريد_الالكتروني', // email
            'الجنس', // gender (ذكر/أنثى)
            'العمر', // age
            'المهنة', // occupation
            'الدولة', // country
            'المدينة', // city
            'الحي', // neighborhood
            'الحالة_الصحية', // health_condition
            'المبلغ_المدفوع', // amount_paid
            'طريقة_الدفع', // payment_method
            'حالة_الدفع', // payment_status (paid/unpaid/pending)
            'الموظف_المخصص', // assigned_employee
            'التقييم', // feedback
            'النقاط', // rating (1-5)
            'تاريخ_الميلاد', // birthdate
            'ملاحظات_اضافية', // additional notes (dynamic field example)
        ];

        // Sample data
        $sampleData = [
            [
                'أحمد محمد',
                '**********',
                '<EMAIL>',
                'ذكر',
                '30',
                'مهندس',
                'السعودية',
                'الرياض',
                'النخيل',
                'جيدة',
                '100.50',
                'بطاقة ائتمان',
                'paid',
                'سارة أحمد',
                'عميل ممتاز',
                '5',
                '1993-05-15',
                'عميل مميز يحتاج متابعة خاصة'
            ],
            [
                'فاطمة علي',
                '0559876543',
                '<EMAIL>',
                'أنثى',
                '25',
                'طبيبة',
                'السعودية',
                'جدة',
                'الحمراء',
                'ممتازة',
                '0',
                '',
                'unpaid',
                'محمد خالد',
                '',
                '',
                '1998-12-20',
                'تحتاج متابعة للدفع'
            ]
        ];

        $data = array_merge([$headers], $sampleData);

        return Excel::download(new class($data) implements \Maatwebsite\Excel\Concerns\FromArray {
            private $data;

            public function __construct($data) {
                $this->data = $data;
            }

            public function array(): array {
                return $this->data;
            }
        }, 'قالب-استيراد-العملاء.xlsx');
    }

    /**
     * Get client statistics for AJAX
     */
    public function statistics()
    {
        $stats = [
            'total' => Client::count(),
            'message_received' => Client::messageReceived()->count(),
            'gift_received' => Client::giftReceived()->count(),
            'paid' => Client::paid()->count(),
            'demographics' => [
                'male' => Client::gender('ذكر')->count(),
                'female' => Client::gender('أنثى')->count(),
            ],
            'age_groups' => [
                '18-25' => Client::ageRange(18, 25)->count(),
                '26-35' => Client::ageRange(26, 35)->count(),
                '36-45' => Client::ageRange(36, 45)->count(),
                '46-60' => Client::ageRange(46, 60)->count(),
                '60+' => Client::where('age', '>', 60)->count(),
            ],
            'top_cities' => Client::selectRaw('city, COUNT(*) as count')
                ->whereNotNull('city')
                ->groupBy('city')
                ->orderByDesc('count')
                ->limit(10)
                ->get(),
            'payment_status' => [
                'pending' => Client::where('payment_status', 'pending')->count(),
                'paid' => Client::where('payment_status', 'paid')->count(),
                'failed' => Client::where('payment_status', 'failed')->count(),
                'refunded' => Client::where('payment_status', 'refunded')->count(),
            ]
        ];

        return response()->json($stats);
    }

    /**
     * Bulk actions for clients
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:delete,activate,deactivate,mark_paid',
            'clients' => 'required|array',
            'clients.*' => 'exists:clients,id'
        ]);

        $clients = Client::whereIn('id', $request->clients);
        $count = $clients->count();

        switch ($request->action) {
            case 'activate':
                $clients->update(['status' => 'active']);
                $message = "تم تفعيل {$count} عميل";
                break;

            case 'deactivate':
                $clients->update(['status' => 'inactive']);
                $message = "تم إلغاء تفعيل {$count} عميل";
                break;

            case 'mark_paid':
                $clients->update([
                    'payment_status' => 'paid',
                    'payment_date' => now()
                ]);
                $message = "تم تسديد {$count} عميل";
                break;

            case 'delete':
                $clients->delete();
                $message = "تم حذف {$count} عميل";
                break;

            default:
                return response()->json([
                    'success' => false,
                    'message' => 'إجراء غير صحيح'
                ], 400);
        }

        return response()->json([
            'success' => true,
            'message' => $message
        ]);
    }

    /**
     * Remove a dynamic field from all clients
     */
    public function removeDynamicField(Request $request)
    {
        $request->validate([
            'field_name' => 'required|string'
        ]);

        $fieldName = $request->field_name;
        $clients = Client::whereNotNull('additional_fields')->get();
        $removedCount = 0;

        foreach ($clients as $client) {
            if ($client->additional_fields && isset($client->additional_fields[$fieldName])) {
                $additionalFields = $client->additional_fields;
                unset($additionalFields[$fieldName]);
                
                $client->update(['additional_fields' => $additionalFields]);
                $removedCount++;
            }
        }

        return response()->json([
            'success' => true,
            'message' => "تم حذف العمود '{$fieldName}' من {$removedCount} عميل"
        ]);
    }
}
