<?php

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use App\Models\GiftCampaign;
use App\Models\Gift;
use App\Models\Client;
use App\Models\MessageTemplate;
use App\Models\GiftDelivery;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CampaignController extends Controller
{
    /**
     * Display a listing of campaigns
     */
    public function index()
    {
        $vendor = Auth::user()->vendor;
        $campaigns = GiftCampaign::where('vendor_id', $vendor->id)
                    ->with(['gift', 'messageTemplate', 'creator'])
                    ->orderBy('created_at', 'desc')
                    ->paginate(10);

        $stats = [
            'total' => GiftCampaign::where('vendor_id', $vendor->id)->count(),
            'pending_approval' => GiftCampaign::where('vendor_id', $vendor->id)->where('stage', 'pending_approval')->count(),
            'in_progress' => GiftCampaign::where('vendor_id', $vendor->id)->whereIn('stage', ['approved', 'processing'])->count(),
            'completed' => GiftCampaign::where('vendor_id', $vendor->id)->where('stage', 'completed')->count(),
        ];

        return view('vendor.campaigns.index', compact('campaigns', 'stats'));
    }

    /**
     * Show the form for creating a new campaign
     */
    public function create()
    {
        $vendor = Auth::user()->vendor;
        $gifts = Gift::where('vendor_id', $vendor->id)
                    ->where('approved', true)
                    ->get();

        if ($gifts->isEmpty()) {
            return redirect()->route('vendor.campaigns.index')
                           ->with('error', 'يجب أن تكون لديك هدايا معتمدة لإنشاء حملة');
        }

        return view('vendor.campaigns.create', compact('gifts'));
    }

    /**
     * Store a newly created campaign
     */
    public function store(Request $request)
    {
        $request->validate([
            'gift_id' => 'required|exists:gifts,id',
            'campaign_name' => 'required|string|max:255',
            'campaign_type' => 'required|in:promotional,seasonal,loyalty,birthday,appreciation',
            'campaign_description' => 'nullable|string|max:1000',
        ]);

        $vendor = Auth::user()->vendor;

        // Make sure the gift belongs to this vendor
        $gift = Gift::where('id', $request->gift_id)
                   ->where('vendor_id', $vendor->id)
                   ->where('approved', true)
                   ->first();

        if (!$gift) {
            return redirect()->back()->withErrors(['gift_id' => 'الهدية غير موجودة أو غير معتمدة']);
        }

        $campaign = GiftCampaign::create([
            'vendor_id' => $vendor->id,
            'gift_id' => $request->gift_id,
            'campaign_name' => $request->campaign_name,
            'campaign_type' => $request->campaign_type,
            'campaign_description' => $request->campaign_description,
            'stage' => 'template_selection',
            'created_by' => auth()->id(),
            'requires_approval' => true,
        ]);

        return redirect()->route('vendor.campaigns.wizard', $campaign)
                        ->with('success', 'تم إنشاء الحملة بنجاح. يرجى المتابعة مع الخطوات التالية.');
    }

    /**
     * Campaign wizard - shows current stage
     */
    public function wizard(GiftCampaign $campaign)
    {
        $vendor = Auth::user()->vendor;
        
        // Check if campaign belongs to vendor
        if ($campaign->vendor_id !== $vendor->id) {
            abort(403, 'غير مصرح لك بعرض هذه الحملة');
        }

        $campaign->load(['gift', 'messageTemplate']);
        
        // Load data needed for wizard stages
        $gifts = Gift::where('vendor_id', $vendor->id)
                    ->where('approved', true)
                    ->get();
        $messageTemplates = MessageTemplate::all();
        
        // Calculate filtered clients count
        $filteredClientsCount = 0;
        if ($campaign->client_filters) {
            $clientQuery = Client::query();
            $filters = is_string($campaign->client_filters) ? json_decode($campaign->client_filters, true) : $campaign->client_filters;
            
            foreach ($filters as $key => $value) {
                if ($key === 'age_from') {
                    $clientQuery->where('age', '>=', $value);
                } elseif ($key === 'age_to') {
                    $clientQuery->where('age', '<=', $value);
                } else {
                    $clientQuery->where($key, $value);
                }
            }
            
            $filteredClientsCount = $clientQuery->count();
            
            // Update the campaign's target_client_count in real-time
            if ($campaign->target_client_count != $filteredClientsCount) {
                $campaign->update(['target_client_count' => $filteredClientsCount]);
            }
        } else {
            $filteredClientsCount = Client::count();
            
            // Update the campaign's target_client_count in real-time
            if ($campaign->target_client_count != $filteredClientsCount) {
                $campaign->update(['target_client_count' => $filteredClientsCount]);
            }
        }

        return view('vendor.campaigns.wizard', compact('campaign', 'gifts', 'messageTemplates', 'filteredClientsCount'));
    }

    /**
     * Update campaign stage
     */
    public function updateStage(Request $request, GiftCampaign $campaign)
    {
        $vendor = Auth::user()->vendor;
        
        if ($campaign->vendor_id !== $vendor->id) {
            abort(403, 'غير مصرح لك بتعديل هذه الحملة');
        }

        switch ($campaign->stage) {
            case 'template_selection':
                return $this->updateTemplateSelection($request, $campaign);
                
            case 'client_filtering':
                return $this->updateClientFiltering($request, $campaign);
                
            case 'message_count':
                return $this->updateMessageCount($request, $campaign);
                
            case 'platform_selection':
                return $this->updatePlatformSelection($request, $campaign);
        }

        return back()->with('error', 'مرحلة غير صحيحة.');
    }

    private function updateTemplateSelection(Request $request, GiftCampaign $campaign)
    {
        $request->validate([
            'template_type' => 'required|in:prebuilt,custom',
            'message_template_id' => 'required_if:template_type,prebuilt|nullable|exists:message_templates,id',
            'custom_message' => 'required_if:template_type,custom|nullable|string|max:1000',
            'custom_media_type' => 'nullable|in:image,video,audio',
            'custom_media_file' => 'nullable|file|mimes:jpg,jpeg,png,mp4,mp3,wav|max:10240',
        ]);

        $updateData = [
            'template_type' => $request->template_type,
            'stage' => 'client_filtering',
        ];

        if ($request->template_type === 'prebuilt') {
            $updateData['message_template_id'] = $request->message_template_id;
            $updateData['custom_message'] = null;
            $updateData['custom_media_type'] = null;
            $updateData['custom_media_path'] = null;
        } else {
            $updateData['custom_message'] = $request->custom_message;
            $updateData['custom_media_type'] = $request->custom_media_type;
            $updateData['message_template_id'] = null;
            
            if ($request->hasFile('custom_media_file')) {
                $file = $request->file('custom_media_file');
                $filename = time() . '-' . $file->getClientOriginalName();
                $path = $file->storeAs('campaigns/media', $filename, 'public');
                $updateData['custom_media_path'] = '/storage/' . $path;
            }
        }

        $campaign->update($updateData);

        return redirect()->route('vendor.campaigns.wizard', $campaign);
    }

    private function updateClientFiltering(Request $request, GiftCampaign $campaign)
    {
        $filters = [];
        
        if ($request->filled('age_from')) {
            $filters['age_from'] = $request->age_from;
        }
        if ($request->filled('age_to')) {
            $filters['age_to'] = $request->age_to;
        }
        if ($request->filled('gender')) {
            $filters['gender'] = $request->gender;
        }
        if ($request->filled('city')) {
            $filters['city'] = $request->city;
        }
        if ($request->filled('country')) {
            $filters['country'] = $request->country;
        }

        // Calculate target client count based on filters
        $clientQuery = Client::query();
        foreach ($filters as $key => $value) {
            if ($key === 'age_from') {
                $clientQuery->where('age', '>=', $value);
            } elseif ($key === 'age_to') {
                $clientQuery->where('age', '<=', $value);
            } else {
                $clientQuery->where($key, $value);
            }
        }
        
        $targetCount = $clientQuery->count();

        $campaign->update([
            'client_filters' => $filters,
            'target_client_count' => $targetCount,
            'stage' => 'message_count',
        ]);

        return redirect()->route('vendor.campaigns.wizard', $campaign);
    }

    private function updateMessageCount(Request $request, GiftCampaign $campaign)
    {
        $request->validate([
            'message_count' => 'required|integer|min:1|max:' . $campaign->target_client_count,
        ]);

        $campaign->update([
            'message_count' => $request->message_count,
            'stage' => 'platform_selection',
        ]);

        return redirect()->route('vendor.campaigns.wizard', $campaign);
    }

    private function updatePlatformSelection(Request $request, GiftCampaign $campaign)
    {
        $request->validate([
            'platform' => 'required|in:own,external',
            'whatsapp_token' => 'required_if:platform,external|nullable|string',
            'messaging_channel' => 'required|in:whatsapp,email,sms',
        ]);

        $updateData = [
            'platform' => $request->platform,
            'messaging_channel' => $request->messaging_channel,
            'stage' => 'pending_approval',
            'requires_approval' => true,
        ];

        if ($request->platform === 'external') {
            $updateData['whatsapp_token'] = $request->whatsapp_token;
        }

        // Calculate total cost
        $giftPrice = $campaign->gift->price;
        $messageCount = $campaign->message_count;
        $messagingCost = 0.5; // Cost per message
        
        $updateData['total_cost'] = ($giftPrice * $messageCount) + ($messagingCost * $messageCount);

        $campaign->update($updateData);

        return redirect()->route('vendor.campaigns.index')
                        ->with('success', 'تم إرسال الحملة للإدارة للموافقة عليها');
    }

    /**
     * Display the specified campaign
     */
    public function show(GiftCampaign $campaign)
    {
        $vendor = Auth::user()->vendor;
        
        if ($campaign->vendor_id !== $vendor->id) {
            abort(403, 'غير مصرح لك بعرض هذه الحملة');
        }

        $campaign->load(['gift', 'messageTemplate', 'creator']);
        
        return view('vendor.campaigns.show', compact('campaign'));
    }

    /**
     * Go back to previous stage
     */
    public function goBackStage(GiftCampaign $campaign)
    {
        $vendor = Auth::user()->vendor;
        
        if ($campaign->vendor_id !== $vendor->id) {
            abort(403, 'غير مصرح لك بتعديل هذه الحملة');
        }

        $stageOrder = [
            'gift_info',
            'template_selection', 
            'client_filtering',
            'message_count',
            'platform_selection',
            'pending_approval'
        ];

        $currentIndex = array_search($campaign->stage, $stageOrder);
        
        if ($currentIndex === false || $currentIndex <= 0) {
            return back()->with('error', 'لا يمكن العودة للمرحلة السابقة.');
        }

        if (in_array($campaign->stage, ['approved', 'processing', 'completed', 'failed'])) {
            return back()->with('error', 'لا يمكن العودة للمرحلة السابقة من هذه المرحلة.');
        }

        $previousStage = $stageOrder[$currentIndex - 1];
        
        $campaign->update([
            'stage' => $previousStage
        ]);

        return redirect()->route('vendor.campaigns.wizard', $campaign)
                        ->with('success', 'تم الانتقال للمرحلة السابقة بنجاح.');
    }

    /**
     * Get filtered clients count via AJAX
     */
    public function getFilteredClientsCount(Request $request)
    {
        $vendor = Auth::user()->vendor;
        
        // Build query based on filters
        $clientQuery = Client::query();
        
        if ($request->filled('age_from')) {
            $clientQuery->where('age', '>=', $request->age_from);
        }
        if ($request->filled('age_to')) {
            $clientQuery->where('age', '<=', $request->age_to);
        }
        if ($request->filled('gender')) {
            $clientQuery->where('gender', $request->gender);
        }
        if ($request->filled('city')) {
            $clientQuery->where('city', $request->city);
        }
        if ($request->filled('country')) {
            $clientQuery->where('country', $request->country);
        }
        
        $count = $clientQuery->count();
        
        return response()->json([
            'count' => $count,
            'message' => $count > 0 ? "تم العثور على {$count} عميل" : 'لا يوجد عملاء مطابقون للفلاتر المحددة'
        ]);
    }

    /**
     * Show campaign deliveries
     */
    public function deliveries(GiftCampaign $campaign)
    {
        $vendor = Auth::user()->vendor;
        
        if ($campaign->vendor_id !== $vendor->id) {
            abort(403, 'غير مصرح لك بعرض تسليمات هذه الحملة');
        }

        $deliveries = GiftDelivery::where('campaign_id', $campaign->id)
                                 ->with(['client', 'employee', 'gift'])
                                 ->orderBy('created_at', 'desc')
                                 ->paginate(20);

        $stats = [
            'total_deliveries' => $deliveries->total(),
            'delivered' => GiftDelivery::where('campaign_id', $campaign->id)->where('status', 'delivered')->count(),
            'pending' => GiftDelivery::where('campaign_id', $campaign->id)->where('status', 'pending')->count(),
            'cancelled' => GiftDelivery::where('campaign_id', $campaign->id)->where('status', 'cancelled')->count(),
        ];

        return view('vendor.campaigns.deliveries', compact('campaign', 'deliveries', 'stats'));
    }

    /**
     * Campaign analytics
     */
    public function analytics(GiftCampaign $campaign)
    {
        $vendor = Auth::user()->vendor;
        
        if ($campaign->vendor_id !== $vendor->id) {
            abort(403, 'غير مصرح لك بعرض إحصائيات هذه الحملة');
        }

        return view('vendor.campaigns.analytics', compact('campaign'));
    }

    /**
     * Campaign performance report
     */
    public function performance()
    {
        $vendor = Auth::user()->vendor;
        
        $campaigns = GiftCampaign::where('vendor_id', $vendor->id)
                    ->with(['gift'])
                    ->get();

        $totalCampaigns = $campaigns->count();
        $totalCost = $campaigns->sum('total_cost');
        $totalMessagesSent = $campaigns->sum('sent_count');
        $avgSuccessRate = $campaigns->avg(function($campaign) {
            $total = $campaign->sent_count + $campaign->failed_count;
            return $total > 0 ? ($campaign->sent_count / $total) * 100 : 0;
        });

        $performance = [
            'total_campaigns' => $totalCampaigns,
            'total_cost' => $totalCost,
            'total_messages_sent' => $totalMessagesSent,
            'avg_success_rate' => round($avgSuccessRate, 2)
        ];

        return view('vendor.campaigns.performance', compact('campaigns', 'performance'));
    }
} 