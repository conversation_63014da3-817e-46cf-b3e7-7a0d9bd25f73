<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // No-op: The initial create migration already defines these columns.
        // Keeping this migration for history while avoiding duplicate column errors in tests.
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // No-op: Nothing to rollback since up() made no changes.
    }
};
