<?php

use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\VendorController;
use App\Http\Controllers\Admin\ClientController;
use App\Http\Controllers\Admin\GiftController;
use App\Http\Controllers\Admin\GiftCampaignController;
use App\Http\Controllers\PaymentController;
use App\Http\Middleware\AdminAuth;

// Vendor Controllers
use App\Http\Controllers\Vendor\DashboardController as VendorDashboardController;
use App\Http\Controllers\Vendor\EmployeeController as VendorEmployeeController;
use App\Http\Controllers\Vendor\GiftController as VendorGiftController;
use App\Http\Controllers\Vendor\CampaignController as VendorCampaignController;

// Landing Page
Route::get('/', function () {
    return view('landing');
});

// Debug route to check user role (only available in local/testing)
if (app()->environment(['local', 'testing'])) {
    Route::get('/debug-user', function () {
        if (!Auth::check()) {
            return 'User not logged in';
        }

        $user = Auth::user();
        return [
            'id' => $user->id,
            'email' => $user->email,
            'role' => $user->role,
            'isAdmin' => method_exists($user, 'isAdmin') ? $user->isAdmin() : null,
            'isVendor' => method_exists($user, 'isVendor') ? $user->isVendor() : null,
            'isStaff' => method_exists($user, 'isStaff') ? $user->isStaff() : null,
        ];
    })->middleware('auth');
}

// Default Breeze Dashboard - with role-based redirection
Route::get('/dashboard', function () {
    $user = Auth::user();
    
    if ($user->isAdmin()) {
        return redirect()->route('admin.dashboard');
    } elseif ($user->isVendor()) {
        return redirect()->route('vendor.dashboard');
    } elseif ($user->isStaff()) {
        return redirect()->route('employee.dashboard');
    }
    
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

// Profile routes
Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

// Admin Routes
Route::prefix('admin')->name('admin.')->middleware(['auth', 'verified', AdminAuth::class])->group(function () {
    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/analytics', [DashboardController::class, 'analytics'])->name('analytics');
    
    // Vendor Management
    Route::resource('vendors', VendorController::class);
    Route::post('vendors/{vendor}/approve', [VendorController::class, 'approve'])->name('vendors.approve');
    Route::post('vendors/{vendor}/reject', [VendorController::class, 'reject'])->name('vendors.reject');
    Route::post('vendors/bulk-action', [VendorController::class, 'bulkAction'])->name('vendors.bulk-action');
    
    // Client Management
            Route::resource('clients', ClientController::class);
        Route::get('clients/export', [ClientController::class, 'export'])->name('clients.export');
        Route::get('clients/template', [ClientController::class, 'downloadTemplate'])->name('clients.template');
        Route::post('clients/import', [ClientController::class, 'import'])->name('clients.import');
        Route::post('clients/{client}/status', [ClientController::class, 'updateStatus'])->name('clients.status');
        Route::post('clients/{client}/mark-paid', [ClientController::class, 'markAsPaid'])->name('clients.mark-paid');
        Route::post('clients/bulk-action', [ClientController::class, 'bulkAction'])->name('clients.bulk-action');
        Route::post('clients/remove-dynamic-field', [ClientController::class, 'removeDynamicField'])->name('clients.remove-dynamic-field');
    
    // Gift Management
    Route::resource('gifts', GiftController::class);
    Route::post('gifts/{gift}/approve', [GiftController::class, 'approve'])->name('gifts.approve');
    Route::post('gifts/{gift}/reject', [GiftController::class, 'reject'])->name('gifts.reject');
    Route::post('gifts/bulk-action', [GiftController::class, 'bulkAction'])->name('gifts.bulk-action');
    
    // Gift Campaign Management
    Route::resource('campaigns', GiftCampaignController::class);
    Route::get('campaigns/{campaign}/wizard', [GiftCampaignController::class, 'wizard'])->name('campaigns.wizard');
    Route::post('campaigns/{campaign}/update-stage', [GiftCampaignController::class, 'updateStage'])->name('campaigns.update-stage');
    Route::post('campaigns/{campaign}/go-back-stage', [GiftCampaignController::class, 'goBackStage'])->name('campaigns.go-back-stage');
    Route::post('campaigns/{campaign}/approve', [GiftCampaignController::class, 'approve'])->name('campaigns.approve');
    Route::post('campaigns/{campaign}/reject', [GiftCampaignController::class, 'reject'])->name('campaigns.reject');
    Route::post('campaigns/{campaign}/execute', [GiftCampaignController::class, 'execute'])->name('campaigns.execute');
    Route::get('campaigns-pending', [GiftCampaignController::class, 'pending'])->name('campaigns.pending');
    Route::get('campaigns-financial-reports', [GiftCampaignController::class, 'financialReports'])->name('campaigns.financial-reports');
    Route::get('vendor-financial-details/{vendor}', [GiftCampaignController::class, 'vendorFinancialDetails'])->name('campaigns.vendor-financial-details');
    Route::get('campaigns/filtered-clients', [GiftCampaignController::class, 'getFilteredClients'])->name('campaigns.filtered-clients');
    Route::get('vendors/{vendor}/gifts', [GiftCampaignController::class, 'getVendorGifts'])->name('vendors.gifts');
    
    // Payment Management
    Route::resource('payments', \App\Http\Controllers\Admin\PaymentController::class)->only(['index', 'show', 'destroy']);
    Route::get('payments-analytics', [\App\Http\Controllers\Admin\PaymentController::class, 'analytics'])->name('payments.analytics');
    Route::get('payments-export', [\App\Http\Controllers\Admin\PaymentController::class, 'export'])->name('payments.export');
    Route::post('payments/{payment}/update-status', [\App\Http\Controllers\Admin\PaymentController::class, 'updateStatus'])->name('payments.update-status');
    Route::post('payments/bulk-action', [\App\Http\Controllers\Admin\PaymentController::class, 'bulkAction'])->name('payments.bulk-action');
});

// Vendor Routes (for authenticated vendors)
Route::prefix('vendor')->name('vendor.')->middleware([App\Http\Middleware\VendorAuth::class])->group(function () {
    // Dashboard
    Route::get('/dashboard', [VendorDashboardController::class, 'index'])->name('dashboard');
    Route::get('/analytics', [VendorDashboardController::class, 'analytics'])->name('analytics');
    Route::post('/track-delivery', [VendorDashboardController::class, 'trackDelivery'])->name('track-delivery');
    Route::get('/notifications', [VendorDashboardController::class, 'notifications'])->name('notifications');
    
    // Employee Management
    Route::resource('employees', VendorEmployeeController::class);
    Route::post('employees/{employee}/update-status', [VendorEmployeeController::class, 'updateStatus'])->name('employees.update-status');
    Route::post('employees/{employee}/update-performance', [VendorEmployeeController::class, 'updatePerformance'])->name('employees.update-performance');
    Route::get('employees/{employee}/deliveries', [VendorEmployeeController::class, 'deliveries'])->name('employees.deliveries');
    Route::get('employees/{employee}/performance-report', [VendorEmployeeController::class, 'performanceReport'])->name('employees.performance-report');
    Route::post('employees/bulk-action', [VendorEmployeeController::class, 'bulkAction'])->name('employees.bulk-action');
    Route::get('employees-export', [VendorEmployeeController::class, 'export'])->name('employees.export');
    
    // Gift Management (Vendor's own gifts)
    Route::resource('gifts', VendorGiftController::class);
    Route::post('gifts/{gift}/submit-for-approval', [VendorGiftController::class, 'submitForApproval'])->name('gifts.submit-for-approval');
    Route::post('gifts/{gift}/update-stock', [VendorGiftController::class, 'updateStock'])->name('gifts.update-stock');
    Route::get('gifts/{gift}/stock-history', [VendorGiftController::class, 'stockHistory'])->name('gifts.stock-history');
    Route::post('gifts/bulk-action', [VendorGiftController::class, 'bulkAction'])->name('gifts.bulk-action');
    Route::get('gifts-export', [VendorGiftController::class, 'export'])->name('gifts.export');
    
    // Campaign Management (Vendor's campaigns)
    Route::resource('campaigns', VendorCampaignController::class);
    Route::get('campaigns/{campaign}/wizard', [VendorCampaignController::class, 'wizard'])->name('campaigns.wizard');
    Route::post('campaigns/{campaign}/update-stage', [VendorCampaignController::class, 'updateStage'])->name('campaigns.update-stage');
    Route::post('campaigns/{campaign}/go-back-stage', [VendorCampaignController::class, 'goBackStage'])->name('campaigns.go-back-stage');
    Route::get('campaigns/{campaign}/deliveries', [VendorCampaignController::class, 'deliveries'])->name('campaigns.deliveries');
    Route::get('campaigns/{campaign}/analytics', [VendorCampaignController::class, 'analytics'])->name('campaigns.analytics');
    Route::get('campaigns-performance', [VendorCampaignController::class, 'performance'])->name('campaigns.performance');
    Route::post('campaigns/filtered-clients-count', [VendorCampaignController::class, 'getFilteredClientsCount'])->name('campaigns.filtered-clients-count');
    
    // Delivery Management
    Route::prefix('deliveries')->name('deliveries.')->group(function () {
        Route::get('/', [VendorEmployeeController::class, 'deliveriesIndex'])->name('index');
        Route::get('/{delivery}', [VendorEmployeeController::class, 'deliveryShow'])->name('show');
        Route::post('/{delivery}/assign-employee', [VendorEmployeeController::class, 'assignEmployee'])->name('assign-employee');
        Route::post('/{delivery}/mark-delivered', [VendorEmployeeController::class, 'markDelivered'])->name('mark-delivered');
        Route::post('/{delivery}/mark-cancelled', [VendorEmployeeController::class, 'markCancelled'])->name('mark-cancelled');
        Route::get('/{delivery}/qr-code', [VendorEmployeeController::class, 'generateQrCode'])->name('qr-code');
        Route::get('/track/{code}', [VendorEmployeeController::class, 'trackByCode'])->name('track');
    });
    
    // Reports & Analytics
    Route::prefix('reports')->name('reports.')->group(function () {
        Route::get('/dashboard', [VendorDashboardController::class, 'reportsIndex'])->name('index');
        Route::get('/employee-performance', [VendorEmployeeController::class, 'performanceReport'])->name('employee-performance');
        Route::get('/delivery-analytics', [VendorDashboardController::class, 'deliveryAnalytics'])->name('delivery-analytics');
        Route::get('/campaign-performance', [VendorCampaignController::class, 'performanceReport'])->name('campaign-performance');
        Route::get('/financial-summary', [VendorDashboardController::class, 'financialSummary'])->name('financial-summary');
    });
});

// Public Payment Routes
Route::prefix('payments')->name('payments.')->group(function () {
    Route::post('/initiate', [PaymentController::class, 'initiate'])->name('initiate');
    Route::get('/callback', [PaymentController::class, 'callback'])->name('callback');
    Route::post('/webhook', [PaymentController::class, 'webhook'])->middleware('throttle:30,1')->name('webhook');
    Route::get('/status/{paymentId}', [PaymentController::class, 'status'])->name('status');
});

// Public Delivery Verification Routes
Route::prefix('delivery')->name('delivery.')->group(function () {
    Route::get('/verify/{code}', function($code) {
        return view('delivery.verify', compact('code'));
    })->name('verify');
    Route::post('/verify/{code}', [VendorEmployeeController::class, 'verifyDelivery'])->name('verify.submit');
});

// API Routes for AJAX requests
Route::prefix('api')->name('api.')->group(function () {
    // Admin API
    Route::middleware(['auth'])->group(function () {
        Route::get('/dashboard/analytics', [DashboardController::class, 'analytics'])->name('dashboard.analytics');
    });
    
    // Vendor API
    Route::middleware([App\Http\Middleware\VendorAuth::class])->group(function () {
        Route::get('/vendor/dashboard/analytics', [VendorDashboardController::class, 'analytics'])->name('vendor.dashboard.analytics');
        Route::get('/vendor/employees/search', [VendorEmployeeController::class, 'search'])->name('vendor.employees.search');
        Route::get('/vendor/deliveries/stats', [VendorEmployeeController::class, 'deliveryStats'])->name('vendor.deliveries.stats');
    });
});

// Employee Routes (Mobile-First Design)
Route::prefix('employee')->name('employee.')->group(function () {
    // Auth routes
    Route::get('/login', [\App\Http\Controllers\Auth\EmployeeAuthController::class, 'showLogin'])->name('login');
    Route::post('/login', [\App\Http\Controllers\Auth\EmployeeAuthController::class, 'login'])->name('login.submit');
    Route::post('/logout', [\App\Http\Controllers\Auth\EmployeeAuthController::class, 'logout'])->name('logout');
    
    // Protected routes - support both employee guard and web guard for staff users
    Route::middleware(['auth'])->group(function () {
        Route::get('/dashboard', [\App\Http\Controllers\EmployeeController::class, 'dashboard'])->name('dashboard');
        Route::get('/search', [\App\Http\Controllers\EmployeeController::class, 'search'])->name('search');
        Route::post('/search', [\App\Http\Controllers\EmployeeController::class, 'search'])->name('search.submit');
        Route::post('/deliveries/{delivery}/confirm', [\App\Http\Controllers\EmployeeController::class, 'confirmDelivery'])->name('deliveries.confirm');
        Route::get('/deliveries/{delivery}/details', [\App\Http\Controllers\EmployeeController::class, 'getDeliveryDetails'])->name('deliveries.details');
        Route::get('/statistics', [\App\Http\Controllers\EmployeeController::class, 'statistics'])->name('statistics');
    });
});

require __DIR__.'/auth.php';
