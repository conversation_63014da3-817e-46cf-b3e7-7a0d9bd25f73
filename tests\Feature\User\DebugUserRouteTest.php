<?php

namespace Tests\Feature\User;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class DebugUserRouteTest extends TestCase
{
    use RefreshDatabase;

    public function test_guest_is_redirected_from_debug_user(): void
    {
        $response = $this->get('/debug-user');
        $response->assertRedirect(route('login', absolute: false));
    }

    public function test_authenticated_user_sees_role_flags_in_debug_user(): void
    {
        $user = User::factory()->create([
            'role' => 'sub_admin',
            'email_verified_at' => now(),
        ]);

        $response = $this->actingAs($user)->get('/debug-user');
        $response->assertStatus(200);
        $response->assertJsonFragment([
            'id' => $user->id,
            'email' => $user->email,
            'role' => 'sub_admin',
            'isAdmin' => false,
            'isVendor' => false,
            'isStaff' => false,
        ]);
    }
}
