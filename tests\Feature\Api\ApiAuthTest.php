<?php

namespace Tests\Feature\Api;

use App\Models\User;
use App\Models\Vendor;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ApiAuthTest extends TestCase
{
    use RefreshDatabase;

    public function test_guest_cannot_access_admin_dashboard_analytics_api(): void
    {
        $response = $this->get(route('api.dashboard.analytics', absolute: false));
        $response->assertRedirect(route('login', absolute: false));
    }

    public function test_authenticated_user_can_access_admin_dashboard_analytics_api(): void
    {
        $user = User::factory()->create(['email_verified_at' => now()]);
        $response = $this->actingAs($user)->get(route('api.dashboard.analytics', absolute: false));
        $response->assertStatus(200);
    }

    public function test_guest_cannot_access_vendor_dashboard_analytics_api(): void
    {
        $response = $this->get(route('api.vendor.dashboard.analytics', absolute: false));
        $response->assertRedirect(route('login', absolute: false));
    }

    public function test_unapproved_vendor_cannot_access_vendor_dashboard_analytics_api(): void
    {
        $user = User::factory()->create(['role' => 'vendor']);
        Vendor::create([
            'user_id' => $user->id,
            'username' => 'vendor_user3',
            'company_name' => 'Gamma Co',
            'email' => $user->email,
            'is_approved' => false,
        ]);

        $response = $this->actingAs($user)->get(route('api.vendor.dashboard.analytics', absolute: false));
        $response->assertStatus(403);
    }

    public function test_approved_vendor_can_access_vendor_dashboard_analytics_api(): void
    {
        $user = User::factory()->create(['role' => 'vendor']);
        Vendor::create([
            'user_id' => $user->id,
            'username' => 'vendor_user4',
            'company_name' => 'Delta Co',
            'email' => $user->email,
            'is_approved' => true,
        ]);

        $response = $this->actingAs($user)->get(route('api.vendor.dashboard.analytics', absolute: false));
        $response->assertStatus(200);
    }
}
