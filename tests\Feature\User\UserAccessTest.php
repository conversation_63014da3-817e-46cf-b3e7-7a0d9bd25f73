<?php

namespace Tests\Feature\User;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class UserAccessTest extends TestCase
{
    use RefreshDatabase;

    public function test_regular_user_can_access_generic_dashboard(): void
    {
        // A regular user is any authenticated user who is not admin/vendor/staff.
        // Use 'sub_admin' which is a valid enum value but not checked in the redirection logic,
        // so it should land on the generic dashboard view.
        $user = User::factory()->create([
            'role' => 'sub_admin',
            'email_verified_at' => now(),
        ]);

        $response = $this->actingAs($user)->get(route('dashboard', absolute: false));

        $response->assertStatus(200);
        $response->assertViewIs('dashboard');
    }

    public function test_unverified_user_is_redirected_from_generic_dashboard(): void
    {
        $user = User::factory()->create([
            'role' => 'sub_admin',
            'email_verified_at' => null,
        ]);

        $response = $this->actingAs($user)->get(route('dashboard', absolute: false));

        $response->assertRedirect(route('verification.notice', absolute: false));
    }

    public function test_guest_is_redirected_when_accessing_generic_dashboard(): void
    {
        $response = $this->get(route('dashboard', absolute: false));

        $response->assertRedirect(route('login', absolute: false));
    }
}
