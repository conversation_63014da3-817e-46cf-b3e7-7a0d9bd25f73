<?php

namespace Tests\Feature\Admin;

use App\Models\Gift;
use App\Models\User;
use App\Models\Vendor;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class GiftManagementTest extends TestCase
{
    use RefreshDatabase;

    protected function actingAsAdmin()
    {
        $admin = User::factory()->create([
            'role' => 'admin',
            'email_verified_at' => now(),
        ]);

        return $this->actingAs($admin);
    }

    protected function createApprovedVendor(): Vendor
    {
        $vendorUser = User::factory()->create([
            'role' => 'vendor',
            'email_verified_at' => now(),
        ]);

        return Vendor::create([
            'user_id' => $vendorUser->id,
            'username' => 'vendor_'.uniqid(),
            'company_name' => 'Test Co',
            'email' => $vendorUser->email,
            'is_approved' => true,
            'password' => 'password',
        ]);
    }

    public function test_admin_can_view_gifts_index(): void
    {
        $this->actingAsAdmin();

        $response = $this->get(route('admin.gifts.index', absolute: false));

        $response->assertStatus(200);
    }

    public function test_store_gift_requires_validation(): void
    {
        $this->actingAsAdmin();

        Storage::fake('public');

        $response = $this->post(route('admin.gifts.store', absolute: false), []);

        $response->assertSessionHasErrors([
            'name', 'description', 'price', 'vendor_id', 'image'
        ]);
    }

    public function test_admin_can_store_gift_with_image(): void
    {
        $this->actingAsAdmin();

        Storage::fake('public');

        // Create a vendor to attach the gift to
        $vendor = $this->createApprovedVendor();

        // Tiny 1x1 PNG image bytes (base64)
        $tinyPng = base64_decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mP8/x8AAwcB9oNnxgAAAABJRU5ErkJggg==');

        $payload = [
            'name' => 'New Gift',
            'description' => 'A nice gift',
            'price' => 99.99,
            'vendor_id' => $vendor->id,
            'category' => 'هدايا أعياد',
            'status' => 'قيد الانتظار',
            'approved' => true,
            'notes' => 'Some notes',
            'image' => UploadedFile::fake()->createWithContent('gift.png', $tinyPng),
        ];

        $response = $this->post(route('admin.gifts.store', absolute: false), $payload);

        $response->assertRedirect(route('admin.gifts.index', absolute: false));
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('gifts', [
            'name' => 'New Gift',
            'vendor_id' => $vendor->id,
            'approved' => 1,
        ]);

        // Ensure file stored
        $gift = Gift::latest('id')->first();
        $this->assertNotNull($gift);
        $this->assertNotNull($gift->image);
    }

    public function test_admin_can_update_gift(): void
    {
        $this->actingAsAdmin();

        $vendor = $this->createApprovedVendor();
        $gift = Gift::create([
            'name' => 'Old Name',
            'description' => 'Old desc',
            'price' => 10,
            'vendor_id' => $vendor->id,
            'category' => 'قديمة',
            'status' => 'قيد الانتظار',
            'approved' => false,
        ]);

        $payload = [
            'name' => 'Updated Name',
            'description' => 'New desc',
            'price' => 20,
            'category' => 'جديدة',
            'status' => 'تم الرفض',
            'approved' => true,
        ];

        $response = $this->put(route('admin.gifts.update', ['gift' => $gift->id], absolute: false), $payload);

        $response->assertRedirect(route('admin.gifts.index', absolute: false));

        $this->assertDatabaseHas('gifts', [
            'id' => $gift->id,
            'name' => 'Updated Name',
            'status' => 'تم الرفض',
            'approved' => 1,
        ]);
    }

    public function test_admin_can_delete_gift(): void
    {
        $this->actingAsAdmin();

        $vendor = $this->createApprovedVendor();
        $gift = Gift::create([
            'name' => 'To Delete',
            'description' => 'Desc',
            'price' => 10,
            'vendor_id' => $vendor->id,
            'status' => 'قيد الانتظار',
            'approved' => false,
        ]);

        $response = $this->delete(route('admin.gifts.destroy', ['gift' => $gift->id], absolute: false));

        $response->assertRedirect(route('admin.gifts.index', absolute: false));
        $this->assertDatabaseMissing('gifts', ['id' => $gift->id]);
    }

    public function test_gifts_index_filters_and_pagination(): void
    {
        $this->actingAsAdmin();

        $vendorA = $this->createApprovedVendor();
        $vendorB = $this->createApprovedVendor();

        // Create gifts for filtering
        Gift::create(['name' => 'A1', 'description' => 'd', 'price' => 5, 'vendor_id' => $vendorA->id, 'status' => 'قيد الانتظار', 'approved' => true]);
        Gift::create(['name' => 'A2 delivered', 'description' => 'd', 'price' => 6, 'vendor_id' => $vendorA->id, 'status' => 'تم التسليم', 'approved' => true]);
        Gift::create(['name' => 'B1 rejected', 'description' => 'd', 'price' => 7, 'vendor_id' => $vendorB->id, 'status' => 'تم الرفض', 'approved' => false]);

        // Pagination: create 13 extra to exceed 15 total when combined with above
        for ($i = 0; $i < 13; $i++) {
            Gift::create(['name' => 'Extra '.$i, 'description' => 'd', 'price' => 1 + $i, 'vendor_id' => $vendorA->id, 'status' => 'قيد الانتظار', 'approved' => true]);
        }

        // Filter by status delivered
        $respStatus = $this->get(route('admin.gifts.index', ['status' => 'تم التسليم'], absolute: false));
        $respStatus->assertStatus(200)->assertSeeText('A2 delivered');

        // Filter by approved=false
        $respApproved = $this->get(route('admin.gifts.index', ['approved' => '0'], absolute: false));
        $respApproved->assertStatus(200)->assertSeeText('B1 rejected');

        // Filter by vendor
        $respVendor = $this->get(route('admin.gifts.index', ['vendor_id' => $vendorA->id], absolute: false));
        $respVendor->assertStatus(200)->assertSeeText('A1');

        // Pagination: page 1 should not show an item we ensure on page 2
        // Make sure we have at least 16 items total
        Gift::create(['name' => 'Sixteenth', 'description' => 'd', 'price' => 99, 'vendor_id' => $vendorA->id, 'status' => 'قيد الانتظار', 'approved' => true]);
        $page1 = $this->get(route('admin.gifts.index', [], absolute: false));
        $page1->assertStatus(200)->assertDontSeeText('Sixteenth');
        $page2 = $this->get(route('admin.gifts.index', ['page' => 2], absolute: false));
        $page2->assertStatus(200)->assertSeeText('Sixteenth');
    }
}
