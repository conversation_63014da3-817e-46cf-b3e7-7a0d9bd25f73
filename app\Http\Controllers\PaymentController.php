<?php

namespace App\Http\Controllers;

use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class PaymentController extends Controller
{
    /**
     * Initiate a payment (stub / placeholder).
     */
    public function initiate(Request $request): Response
    {
        // Validate request as needed and create a Payment record
        // This is a placeholder to avoid exposing implementation details.
        return response(['message' => 'Payment initiation endpoint'], 200);
    }

    /**
     * Handle provider callback/redirect (user-agent return).
     */
    public function callback(Request $request): Response
    {
        // Handle redirect parameters and update UI/state as necessary
        return response(['message' => 'Callback received'], 200);
    }

    /**
     * Webhook endpoint: verifies signature and ensures idempotency.
     */
    public function webhook(Request $request): Response
    {
        $secret = (string) config('services.payments.webhook_secret', env('PAYMENT_WEBHOOK_SECRET'));
        if ($secret === '') {
            Log::warning('Webhook secret not configured');
            return response('Unauthorized', 401);
        }

        $signature = (string) $request->header('X-Signature', '');
        $rawBody = $request->getContent();
        $expected = base64_encode(hash_hmac('sha256', $rawBody, $secret, true));

        if (! hash_equals($expected, $signature)) {
            Log::warning('Invalid webhook signature');
            return response('Unauthorized', 401);
        }

        $payload = $request->json()->all();
        $eventId = (string) data_get($payload, 'id', '');
        if ($eventId === '') {
            Log::warning('Webhook missing event id');
            return response('Bad Request', 400);
        }

        $cacheKey = 'payments:webhook:' . $eventId;
        if (! Cache::add($cacheKey, true, now()->addHours(6))) {
            // Duplicate event; already processed
            return response('OK', 200);
        }

        try {
            // TODO: Map event types to actions, update Payment records accordingly
            $type = (string) data_get($payload, 'type', '');
            Log::info('Webhook processed', ['id' => $eventId, 'type' => $type]);
        } catch (\Throwable $e) {
            Log::error('Webhook processing failed', ['id' => $eventId, 'error' => $e->getMessage()]);
            // Optionally delete idempotency key to allow retry depending on failure type
            return response('Server Error', 500);
        }

        return response('OK', 200);
    }

    /**
     * Get payment status by ID.
     */
    public function status(string $paymentId): Response
    {
        $payment = Payment::query()->findOrFail($paymentId);
        return response([
            'id' => $payment->id,
            'status' => $payment->status ?? null,
            'amount' => $payment->amount ?? null,
            'currency' => $payment->currency ?? null,
            'updated_at' => optional($payment->updated_at)->toISOString(),
        ], 200);
    }
}
