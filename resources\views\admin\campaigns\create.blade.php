@extends('layouts.admin')

@section('title', 'إنشاء حملة جديدة')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-bullhorn me-2"></i>
                    إنشاء حملة إعلانية جديدة
                </h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">لوحة التحكم</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.campaigns.index') }}">الحملات الإعلانية</a></li>
                        <li class="breadcrumb-item active" aria-current="page">إنشاء حملة جديدة</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="create-form-container">
                <div class="create-form-header">
                    <h1>
                        <i class="fas fa-rocket me-2"></i>
                        إنشاء حملة إعلانية جديدة
                    </h1>
                    <p>اختر التاجر والهدية التي تريد الترويج لها وسنساعدك في إنشاء حملة إعلانية فعالة</p>
                </div>

                <div class="create-form-body">

                    <form action="{{ route('admin.campaigns.store') }}" method="POST" class="campaign-form">
                        @csrf

                        <!-- Vendor Selection Section -->
                        <div class="form-section">
                            <h4 class="section-title">
                                <i class="fas fa-store section-icon"></i>
                                اختيار التاجر
                            </h4>
                            
                            <div class="form-group">
                                <label for="vendor_id" class="form-label required">
                                    <i class="fas fa-user-tie me-1"></i>
                                    التاجر
                                </label>
                                <select id="vendor_id" 
                                        name="vendor_id" 
                                        class="form-select @error('vendor_id') is-invalid @enderror" 
                                        required>
                                    <option value="">اختر التاجر</option>
                                    @foreach($vendors as $vendor)
                                        <option value="{{ $vendor->id }}" {{ old('vendor_id') == $vendor->id ? 'selected' : '' }}>
                                            {{ $vendor->name }} - {{ $vendor->email }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('vendor_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    ستظهر هدايا التاجر المختار فقط
                                </div>
                            </div>
                        </div>

                        <!-- Gift Selection Section -->
                        <div class="form-section" id="gift-selection-section" style="display: none;">
                            <h4 class="section-title">
                                <i class="fas fa-gift section-icon"></i>
                                اختيار الهدية
                            </h4>
                            
                            <div class="form-group">
                                <label for="gift_id" class="form-label required">
                                    <i class="fas fa-gift me-1"></i>
                                    الهدية
                                </label>
                                <select id="gift_id" 
                                        name="gift_id" 
                                        class="form-select @error('gift_id') is-invalid @enderror" 
                                        required>
                                    <option value="">اختر الهدية</option>
                                </select>
                                @error('gift_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Gift Preview -->
                            <div class="gift-preview-card" id="gift-preview" style="display: none;">
                                <div class="row align-items-center">
                                    <div class="col-md-8">
                                        <h5 class="gift-name mb-1"></h5>
                                        <p class="gift-description text-muted mb-2"></p>
                                        <div class="gift-details">
                                            <span class="badge bg-success me-2">
                                                <i class="fas fa-money-bill-wave me-1"></i>
                                                السعر: <span class="gift-price"></span> ريال
                                            </span>
                                            <span class="badge bg-info">
                                                <i class="fas fa-boxes me-1"></i>
                                                المتوفر: <span class="gift-stock"></span> قطعة
                                            </span>
                                        </div>
                                    </div>
                                    <div class="col-md-4 text-center">
                                        <div class="gift-image-placeholder">
                                            <i class="fas fa-gift fa-3x text-primary"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="form-actions">
                            <div class="d-flex justify-content-between align-items-center">
                                <a href="{{ route('admin.campaigns.index') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>
                                    إلغاء والعودة
                                </a>
                                
                                <button type="submit" class="btn btn-primary btn-lg" id="continueBtn" disabled>
                                    <i class="fas fa-arrow-right me-2"></i>
                                    إنشاء الحملة والمتابعة
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.campaign-create-card {
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border-radius: 15px;
    overflow: hidden;
}

.campaign-create-card .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    padding: 2rem;
}

.form-section {
    margin-bottom: 2.5rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #667eea;
}

.section-title {
    color: #333;
    margin-bottom: 1.5rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.section-icon {
    color: #667eea;
    font-size: 1.2em;
}

.form-label.required::after {
    content: " *";
    color: #dc3545;
    font-weight: bold;
}

.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 0.75rem;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.gift-preview-card {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    border: 2px dashed #e9ecef;
    transition: all 0.3s ease;
    margin-top: 1rem;
}

.gift-image-placeholder {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 2rem;
    border: 2px dashed #dee2e6;
}

.form-actions {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 2px solid #e9ecef;
}

.btn {
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.page-header {
    margin-bottom: 2rem;
    text-align: center;
}

.page-title {
    color: #333;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.create-form-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
    position: relative;
}

.create-form-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, var(--vendor-secondary) 0%, var(--vendor-accent) 100%);
}

.create-form-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 30px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    text-align: center;
    position: relative;
}

.create-form-header h1 {
    color: var(--vendor-primary);
    font-weight: 700;
    font-size: 2rem;
    margin-bottom: 10px;
}

.create-form-header p {
    color: #6c757d;
    font-size: 1.1rem;
    margin: 0;
}

.create-form-body {
    padding: 40px;
    color: #212529;
}

:root {
    --vendor-primary: #6a1b9a;
    --vendor-secondary: #ff1493;
    --vendor-accent: #ff9800;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const vendorSelect = document.getElementById('vendor_id');
    const giftSelect = document.getElementById('gift_id');
    const giftSelectionSection = document.getElementById('gift-selection-section');
    const giftPreview = document.getElementById('gift-preview');
    const continueBtn = document.getElementById('continueBtn');

    // Handle vendor selection change
    vendorSelect.addEventListener('change', function() {
        const vendorId = this.value;
        
        if (vendorId) {
            // Show gift selection section
            giftSelectionSection.style.display = 'block';
            
            // Clear existing gifts
            giftSelect.innerHTML = '<option value="">جاري التحميل...</option>';
            
            // Fetch vendor gifts
            fetch(`/admin/vendors/${vendorId}/gifts`)
                .then(response => response.json())
                .then(gifts => {
                    giftSelect.innerHTML = '<option value="">اختر الهدية</option>';
                    
                    gifts.forEach(gift => {
                        const option = document.createElement('option');
                        option.value = gift.id;
                        option.textContent = `${gift.name} - ${Number(gift.price).toLocaleString()} ريال`;
                        option.setAttribute('data-price', gift.price);
                        option.setAttribute('data-stock', gift.stock_quantity);
                        option.setAttribute('data-description', gift.description);
                        giftSelect.appendChild(option);
                    });
                })
                .catch(error => {
                    console.error('Error fetching gifts:', error);
                    giftSelect.innerHTML = '<option value="">خطأ في تحميل الهدايا</option>';
                });
        } else {
            giftSelectionSection.style.display = 'none';
            giftPreview.style.display = 'none';
            continueBtn.disabled = true;
        }
    });

    // Handle gift selection change
    giftSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        
        if (this.value) {
            const giftName = selectedOption.text.split(' - ')[0];
            const giftPrice = selectedOption.getAttribute('data-price');
            const giftStock = selectedOption.getAttribute('data-stock');
            const giftDescription = selectedOption.getAttribute('data-description');
            
            document.querySelector('.gift-name').textContent = giftName;
            document.querySelector('.gift-description').textContent = giftDescription;
            document.querySelector('.gift-price').textContent = Number(giftPrice).toLocaleString();
            document.querySelector('.gift-stock').textContent = giftStock;
            
            giftPreview.style.display = 'block';
            giftPreview.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
            
            // Enable continue button
            continueBtn.disabled = false;
        } else {
            giftPreview.style.display = 'none';
            continueBtn.disabled = true;
        }
    });

    // Enhanced form validation
    document.querySelector('.campaign-form').addEventListener('submit', function(e) {
        const requiredFields = ['vendor_id', 'gift_id'];
        let isValid = true;
        
        requiredFields.forEach(field => {
            const element = document.getElementById(field);
            if (!element.value.trim()) {
                element.classList.add('is-invalid');
                isValid = false;
            } else {
                element.classList.remove('is-invalid');
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            alert('يرجى ملء جميع الحقول المطلوبة');
        } else {
            // Show loading state
            continueBtn.innerHTML = `
                <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                جاري الإنشاء...
            `;
            continueBtn.disabled = true;
        }
    });
});
</script>

@endsection 